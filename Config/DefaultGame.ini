

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=DC6A05A5429154940C904FA55C34CB1F
ProjectName=The Awakener: Battle Tendency
Description=The Awakener: Battle Tendency
CompanyName=TanerGame
ProjectVersion=*******
CopyrightNotice=The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd
ProjectDisplayedTitle=NSLOCTEXT("[/Script/EngineSettings]", "1CE65E9043F62FC13682FA90C78AD2C3", "The Awakener: Battle Tendency")
ProjectDebugTitleInfo=NSLOCTEXT("[/Script/EngineSettings]", "0BA9EDB142AFC6CD0A27F9B93B9B4FFC", "The Awakener: Battle Tendency")
SupportContact=<EMAIL>
CompanyDistinguishedName=TanerGame

[/Script/MoviePlayer.MoviePlayerSettings]
bWaitForMoviesToComplete=True
bMoviesAreSkippable=False
+StartupMovies=LogoInto

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_DebugGame
BuildTarget=TheAwakenerRisen
LaunchOnTarget=
StagingDirectory=(Path="E:/Package/2023.04.13")
FullRebuild=True
ForDistribution=False
IncludeDebugFiles=True
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=False
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=True
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=True
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
WriteBackMetadataToAssetRegistry=Disabled
bCompressed=True
PackageCompressionFormat=Oodle
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PackageAdditionalCompressionOptions=
PackageCompressionMethod=Kraken
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionLevel_TestShipping=5
PackageCompressionLevel_Distribution=7
PackageCompressionMinBytesSaved=1024
PackageCompressionMinPercentSaved=5
bPackageCompressionEnableDDC=False
PackageCompressionMinSizeToConsiderDDC=0
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyDenylist=KeyStorePassword
-IniKeyDenylist=KeyPassword
-IniKeyDenylist=rsa.privateexp
-IniKeyDenylist=rsa.modulus
-IniKeyDenylist=rsa.publicexp
-IniKeyDenylist=aes.key
-IniKeyDenylist=SigningPublicExponent
-IniKeyDenylist=SigningModulus
-IniKeyDenylist=SigningPrivateExponent
-IniKeyDenylist=EncryptionKey
-IniKeyDenylist=DevCenterUsername
-IniKeyDenylist=DevCenterPassword
-IniKeyDenylist=IOSTeamID
-IniKeyDenylist=SigningCertificate
-IniKeyDenylist=MobileProvision
-IniKeyDenylist=IniKeyDenylist
-IniKeyDenylist=IniSectionDenylist
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=DevCenterUsername
+IniKeyDenylist=DevCenterPassword
+IniKeyDenylist=IOSTeamID
+IniKeyDenylist=SigningCertificate
+IniKeyDenylist=MobileProvision
+IniKeyDenylist=IniKeyDenylist
+IniKeyDenylist=IniSectionDenylist
-IniSectionDenylist=HordeStorageServers
-IniSectionDenylist=StorageServers
+IniSectionDenylist=HordeStorageServers
+IniSectionDenylist=StorageServers
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Title/Rogue_Title")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Hall/Rogue_Hall")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Hall/LA_Rogue_Hall")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Hall/LA_HallLightmass_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Hall/Rogue_Hall_Script")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Castle/Rogue_Castle_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Castle/LA_Castle_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Castle/LA_Castle_Normal_02_Teach")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Lava/Rogue_Lava_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Lava/LA_Lava_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Desert/Rogue_Desert_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/Desert/LA_Desert_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike/FinalBattle/Rogue_FinalBattle_01")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Hall/Hall_Svl")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Hall/Hall_lightmass_Svl")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Hall/Hall_Script_Svl")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Hall/Hall_Svl_A")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Level/Level_01C")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Level/Level_01C_Script")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Level/Level_01_Light_Old")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Level/Level_01K")
+MapsToCook=(FilePath="/Game/Maps/Roguelike_Svl/Level/Level_01K_Script")
+MapsToCook=(FilePath="/Game/Maps/Debug/MobTest")
+DirectoriesToAlwaysCook=(Path="/Game/Core/FrameWork")
+DirectoriesToAlwaysCook=(Path="/Game/Audio")
+DirectoriesToAlwaysCook=(Path="/Game/DesignerScript")
+DirectoriesToAlwaysCook=(Path="/Game/MaterialLibrary")
+DirectoriesToAlwaysCook=(Path="/Game/Data")
+DirectoriesToAlwaysCook=(Path="/Game/Scene")
+DirectoriesToAlwaysCook=(Path="/Interchange/Functions")
+DirectoriesToAlwaysCook=(Path="/Interchange/gltf")
+DirectoriesToAlwaysCook=(Path="/Interchange/Materials")
+DirectoriesToAlwaysCook=(Path="/Interchange/Pipelines")
+DirectoriesToAlwaysCook=(Path="/Interchange/Utilities")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/Material")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/UI/Roguelike")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Characters/Player_RoguePawn")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Characters/Equipment/Weapons/Rogue")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/AOE")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/Bullet")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/Rouge")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/Monster")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/CreaterController")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Item/Survivor")
+DirectoriesToAlwaysCook=(Path="/Game/Core/WBP")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/Base")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/Roguelike/00-Formal")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/Roguelike_svl/00-Formal")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/Subtitle")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/UIBubbles")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/PopUpText")
+DirectoriesToAlwaysCook=(Path="/Game/Core/UI/Debug")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Interact/Rogue")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Camera")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Utility")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Anim")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/MaterialLibrary")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/VFX")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Sequencer")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Characters/Player/BladeDancer")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Characters/Player/Fighter")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Characters/Player/Vanguard")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Characters/Player/Warrior")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Characters/Rogue_Mob")
+DirectoriesToAlwaysCook=(Path="/Game/Core/Characters/Survivor_Mob")
+DirectoriesToAlwaysCook=(Path="/Game/ArtResource/ProjectRogue/Anim/BlendSpace")
+DirectoriesToNeverCook=(Path="/Game/Core/Utility/FogSetting")
+DirectoriesToNeverCook=(Path="/Game/ArtResource/UI/Roguelike/00-Preview")
+DirectoriesToNeverCook=(Path="/Game/ArtResource/UI/Roguelike/Formal/00-Preview")
+DirectoriesToAlwaysStageAsUFS=(Path="Json")
+DirectoriesToAlwaysStageAsUFS=(Path="Movies")
+DirectoriesToAlwaysStageAsUFS=(Path="MoviesInRogue")
PerPlatformBuildConfig=()
PerPlatformTargetFlavorName=(("Android", "Android_ASTC"))
PerPlatformBuildTarget=()


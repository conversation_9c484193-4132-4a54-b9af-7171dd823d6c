// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "WaveManager.h"

#include "SurvivorEventFunc.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/MonsterReplaceMgr.h"


// Sets default values
AWaveManager::AWaveManager()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

void AWaveManager::AddKillMonster(FString MobId, FString AlterId)
{
	int key = FStringPool::Register(UMonsterReplaceMgr::MakeGroupName(MobId,AlterId));
	if(!CurKillMonsterCount.Contains(key))
	{
		CurKillMonsterCount.Add(key, 0);
	}
	CurKillMonsterCount[key]++;
}

void AWaveManager::AddSpawnMonster(FString MobId, FString AlterId)
{
	int key = FStringPool::Register(UMonsterReplaceMgr::MakeGroupName(MobId,AlterId));
	if(!CurSpawnMonsterCount.Contains(key))
	{
		CurSpawnMonsterCount.Add(key, 0);
	}
	CurSpawnMonsterCount[key]++;
}

int AWaveManager::GetMonsterSpawnAvailability(FString MobId, FString AlterId)
{
	int key = FStringPool::Register(UMonsterReplaceMgr::MakeGroupName(MobId,AlterId));
	auto Spawnset = CurWaveMonster.MonsterSets.FindByPredicate([&](const FCMonsterSpawnSet& SpawnSet)
	{
		return SpawnSet.MobId == MobId && SpawnSet.AlterId == AlterId;
	});
	if (Spawnset && Spawnset->Availability<0)return Spawnset->Availability;
	if(CurSpawnMonsterCount.Contains(key))
	{
		if (Spawnset)
		{
			int rest = Spawnset->Availability - CurSpawnMonsterCount[key];
			if (rest<=0)
			{
				return 0;
			}
			return rest;
		}
		UE_LOG(LogTemp,Log,TEXT("AWaveManager::IsMonsterSpawnable No SpawnSet mactch."))
	}
	if (Spawnset)
	{
		return Spawnset->Availability;
	}
	return 0;
}

TArray<FTransform> AWaveManager::GetPointsByType(EWaveEventPositionType Type,TArray<int> Params)
{
	auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	AAwCharacter* Target = nullptr;
	int points = 0;
	float radius = 0;
	float length = 0;
	if (Params.Num()>0)
	{
		radius = Params[0];
		length = Params[0];
	}
	Target = UGameplayFuncLib::GetLocalAwPlayerCharacter(0);
	if (Params.Num()>1)
	{
		points = Params[1];
	}
	switch (Type)
	{
		case EWaveEventPositionType::NormalPoints:
			return USurvivorEventFunc::PositionFromExistPoints(gs->SpawnData.NormalMonsterSpawnPoint,Params);
		case EWaveEventPositionType::ElitePoints:
			return USurvivorEventFunc::PositionFromExistPoints(gs->SpawnData.EliteMonsterSpawnPoint,Params);
		case EWaveEventPositionType::BossPoints:
			return USurvivorEventFunc::PositionFromExistPoints(gs->SpawnData.BossMonsterSpawnPoint,Params);
		case EWaveEventPositionType::SpecialPoints:
			return USurvivorEventFunc::PositionFromExistPoints(gs->SpawnData.SpecialPoint,Params);
		case EWaveEventPositionType::AroundPlayerCircle:
		case EWaveEventPositionType::AroundTowerCircle:
			if (Type==EWaveEventPositionType::AroundTowerCircle)
			{
				Target = gs->AthenaTower;
			}
			return USurvivorEventFunc::PositionAroundTargetCircle(Target,radius,points);
		case EWaveEventPositionType::AroundPlayerSquare:
		case EWaveEventPositionType::AroundTowerSquare:
			if (Type==EWaveEventPositionType::AroundTowerCircle)
			{
				Target = gs->AthenaTower;
			}
			return USurvivorEventFunc::PositionAroundTargetSquare(Target,length,points);
		case EWaveEventPositionType::BeforeTower:
			UE_LOG(LogTemp,Error,TEXT("AWaveManager::GetPointsByType not implement!"))
		break;
	}
	return TArray<FTransform>();
}

// Called when the game starts or when spawned
void AWaveManager::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AWaveManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}


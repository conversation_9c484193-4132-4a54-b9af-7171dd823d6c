#include "CMonsterSpawnSet.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "Dom/JsonObject.h"

FCMonsterSpawnSet FCMonsterSpawnSet::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCMonsterSpawnSet Result;

	if (JsonObj.IsValid())
	{
		Result.MobId = UDataFuncLib::AwGetStringField(JsonObj, "MobId", "");
		Result.AlterId = UDataFuncLib::AwGetStringField(JsonObj, "AlterId", "");
		Result.RandomWeight = UDataFuncLib::AwGetNumberField(JsonObj, "RandomWeight", 1.0f);
		Result.MobLevel = UDataFuncLib::AwGetNumberField(JsonObj, "MobLevel", 1);
		Result.Availability = UDataFuncLib::AwGetNumberField(JsonObj, "Availability", -1);
	}

	return Result;
}

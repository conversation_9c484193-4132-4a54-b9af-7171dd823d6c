// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "CWaveEvent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FCWaveEvent FCWaveEvent::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCWaveEvent Result;
	if (JsonObj.IsValid())
	{
		Result.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id","");
		Result.TimeLimit = UDataFuncLib::AwGetNumberField(JsonObj,"TimeLimit",-1.0f);
		Result.MonsterLimit = UDataFuncLib::AwGetNumberField(JsonObj,"MonsterLimit",1);
		Result.Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");
		Result.MonsterSetId = UDataFuncLib::AwGetStringField(JsonObj,"MonsterSetId","");
		Result.BossId = UDataFuncLib::AwGetStringField(JsonObj,"BossId","");
		Result.RequireEventComplete = UDataFuncLib::AwGetStringField(JsonObj,"RequireEventComplete","");

		// 解析TargetNum字段
		if (JsonObj->HasField("TargetNum"))
		{
			const TSharedPtr<FJsonObject> TargetNumObj = JsonObj->GetObjectField("TargetNum");
			if (TargetNumObj.IsValid())
			{
				for (const auto& Pair : TargetNumObj->Values)
				{
					// 使用FStringPool.Register获取string的int值作为key
					int32 StringKey = FStringPool::Register(Pair.Key);
					int32 Value = Pair.Value->AsNumber();
					Result.TargetNum.Add(StringKey, Value);
				}
			}
		}
		Result.PositionType = UDataFuncLib::AwGetEnumField(JsonObj, "PositionType", EWaveEventPositionType::NormalPoints);
		// 解析PositionsParams数组
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "PositionsParams"))
		{
			if (Value.IsValid() && Value->Type == EJson::Number)
			{
				Result.PositionsParams.Add(Value->AsNumber());
			}
		}
	}
	return Result;
}

FCWaveMonsterSpawn FCWaveMonsterSpawn::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCWaveMonsterSpawn Result;

	if (JsonObj.IsValid())
	{
		Result.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
		Result.TimeStart = UDataFuncLib::AwGetNumberField(JsonObj, "TimeStart", 0.0f);
		Result.TimeEnd = UDataFuncLib::AwGetNumberField(JsonObj, "TimeEnd", 0.0f);
		Result.Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");

		// 解析MonsterSets数组
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "MonsterSets"))
		{
			if (Value.IsValid() && Value->Type == EJson::Object)
			{
				FCMonsterSpawnSet MonsterSet = FCMonsterSpawnSet::FromJson(Value->AsObject());
				Result.MonsterSets.Add(MonsterSet);
			}
		}

		Result.PositionType = UDataFuncLib::AwGetEnumField(JsonObj, "PositionType", EWaveEventPositionType::NormalPoints);

		// 解析PositionsParams数组
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "PositionsParams"))
		{
			if (Value.IsValid() && Value->Type == EJson::Number)
			{
				Result.PositionsParams.Add(Value->AsNumber());
			}
		}

		Result.RequireEventComplete = UDataFuncLib::AwGetStringField(JsonObj, "RequireEventComplete", "");
	}

	return Result;
}

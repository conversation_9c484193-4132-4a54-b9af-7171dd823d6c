// Fill out your copyright notice in the Description page of Project Settings.

#include "LevelData.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

// FLevelSelectionData 实现

FLevelSelectionData FLevelSelectionData::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FLevelSelectionData Result;
	
	if (JsonObj.IsValid())
	{
		// 读取ID字段
		if (JsonObj-><PERSON><PERSON>ield(TEXT("ID")))
		{
			Result.ID = JsonObj->GetStringField(TEXT("ID"));
		}
		
		// 读取GroupdID字段
		if (JsonObj->HasField(TEXT("GroupdID")))
		{
			Result.GroupdID = JsonObj->GetStringField(TEXT("GroupdID"));
		}
		
		// 读取Type字段
		if (JsonObj->HasField(TEXT("Type")))
		{
			FString TypeString = JsonObj->GetStringField(TEXT("Type"));
			if (TypeString == TEXT("RPG"))
			{
				Result.Type = ELevelType::RPG;
			}
			else if (TypeString == TEXT("Rogue"))
			{
				Result.Type = ELevelType::Rogue;
			}
			else if (TypeString == TEXT("Survivor"))
			{
				Result.Type = ELevelType::Survivor;
			}
			else
			{
				// 默认为Survivor类型
				Result.Type = ELevelType::Survivor;
			}
		}
		// 读取Valid字段
		if (JsonObj->HasField(TEXT("Valid")))
		{
			Result.Valid = JsonObj->GetBoolField(TEXT("Valid"));
		}
		if (JsonObj->HasField(TEXT("LevelPath")))
		{
			Result.LevelPath = JsonObj->GetStringField(TEXT("LevelPath"));
		}
		
	}
	
	return Result;
}
// FLevelIntroduce 实现

FLevelIntroduce FLevelIntroduce::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FLevelIntroduce Result;

	if (JsonObj.IsValid())
	{
		// 读取LevelKey字段
		if (JsonObj->HasField(TEXT("ID")))
		{
			Result.ID = JsonObj->GetStringField(TEXT("ID"));
		}

		// 读取Image字段
		if (JsonObj->HasField(TEXT("Image")))
		{
			Result.Image = JsonObj->GetStringField(TEXT("Image"));
		}

		// 读取Introduce字段
		if (JsonObj->HasField(TEXT("Introduce")))
		{
			TSharedPtr<FJsonValue> IntroduceValue = JsonObj->TryGetField(TEXT("Introduce"));
			if (IntroduceValue.IsValid() && IntroduceValue->Type == EJson::Object)
			{
				// 直接从JsonObject获取所有键值对
				TSharedPtr<FJsonObject> IntroduceObj = IntroduceValue->AsObject();
				for (const auto& Pair : IntroduceObj->Values)
				{
					if (Pair.Value.IsValid())
					{
						Result.Introduce.Add(Pair.Key, Pair.Value->AsString());
					}
				}
			}
		}
	}

	return Result;
}


// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "CWaveEvent.h"
#include "GameFramework/Actor.h"
#include "WaveManager.generated.h"

UCLASS()
class THEAWAKENER_FO_API AWaveManager : public AActor
{
	GENERATED_BODY()
	
public:
	// Sets default values for this actor's properties
	AWaveManager();
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FCWaveEvent CurWave;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FCWaveMonsterSpawn CurWaveMonster;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TMap<int,int> CurKillMonsterCount;
	UFUNCTION(BlueprintCallable)
	void AddKillMonster(FString MobId,FString AlterId);
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	TMap<int,int> CurSpawnMonsterCount;
	UFUNCTION(BlueprintCallable)
	void AddSpawnMonster(FString MobId,FString AlterId);
	UFUNCTION(BlueprintCallable)
	int GetMonsterSpawnAvailability(FString MobId,FString AlterId);
	UFUNCTION(BlueprintCallable)
	static TArray<FTransform> GetPointsByType(EWaveEventPositionType Type,TArray<int> Params);
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;
};

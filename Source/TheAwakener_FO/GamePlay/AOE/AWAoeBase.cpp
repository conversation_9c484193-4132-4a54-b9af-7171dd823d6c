// Fill out your copyright notice in the Description page of Project Settings.


#include "AWAoeBase.h"


#include "Located.h"
#include "Components/StaticMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Timeline/TimelineManager.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/AttackHitBoxSign.h"

// Sets default values
AAWAoe::AAWAoe()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	SceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("SceneComponent"));
	//SceneComponent->SetupAttachment(GetRootComponent());
	SetRootComponent(SceneComponent);
	AttackHitManager = CreateDefaultSubobject<UAttackHitManager>(TEXT("AttackHitManager"));

	CatchMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CatchMesh"));
	CatchMesh->SetupAttachment(SceneComponent);
	//static ConstructorHelpers::FObjectFinder<UStaticMesh> CubeMeshAsset(TEXT("/Game/ArtResource/Shapes/Shape_Cube.Shape_Cube"));
	//CatchMesh->SetStaticMesh(CubeMeshAsset.Object);
	//static ConstructorHelpers::FObjectFinder<UMaterial> CatcherMaterialAsset(TEXT("/Game/MaterialLibrary/Debug/M_Trigger"));
	//CatchMesh->SetMaterial(0, CatcherMaterialAsset.Object);
	//CatchMesh->SetWorldScale3D(FVector(1, 1, 1));

	//Add Mesh AssetUserData  This May Cannot construct because lose its template After Package
	/*
	 EObjectFlags Flags = RF_Public;
	UAttackHitBoxSign* HitBoxSign = NewObject< UAttackHitBoxSign >(CatchMesh, NAME_None, Flags);
	CatchMesh->AddAssetUserData(HitBoxSign);
	*/
	
	ShowMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ShowMesh"));
	ShowMesh->SetupAttachment(SceneComponent);

	//暂时先预设5个特效
	VFX01 = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("VFX01"));
	VFX01->SetupAttachment(SceneComponent);
	VFX02 = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("VFX02"));
	VFX02->SetupAttachment(SceneComponent);
	VFX03 = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("VFX03"));
	VFX03->SetupAttachment(SceneComponent);
	VFX04 = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("VFX04"));
	VFX04->SetupAttachment(SceneComponent);
	VFX05 = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("VFX05"));
	VFX05->SetupAttachment(SceneComponent);

	bReplicates = true;
}

// Called when the game starts or when spawned
void AAWAoe::BeginPlay()
{
	Super::BeginPlay();

	OriginLocation = this->GetActorLocation();
	OriginDirection = this->GetActorForwardVector();
	//需要手动调AttackHitManager->Update，所以不调用Start
	//AttackHitManager->Start();
	AttackHitManager->ActiveAllAttackHitBox();
}

void AAWAoe::Init(FAOEModel InModel, AAwCharacter* InCaster, FChaProp InCasterProp)
{
	Model = InModel;
	AOELifeSpan = Model.AOELifeSpan;
	for (FString CurTag : InModel.Tags)
	{
		Tags.Add(FName(*CurTag)); 
	}
	Caster = InCaster;
	CasterProp = InCasterProp;

	//Call OnCreate
	if (Model.OnCreate.Num())
	{
		UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
		for (int i = 0; i < Model.OnCreate.Num(); i++)
		{
			UFunction* Func;
			Func = UCallFuncLib::GetUFunction(Model.OnCreate[i].ClassPath, Model.OnCreate[i].FunctionName);
			if (Func)
			{
				struct
				{
					AAWAoe* AOEObj;
					TArray<FString> Params;
					UTimelineNode* Result = nullptr;
				}FuncParam;
				FuncParam.AOEObj = this;
				FuncParam.Params = Model.OnCreate[i].Params;
				if (Caster)
					Caster->ProcessEvent(Func, &FuncParam);
				else
					this->ProcessEvent(Func, &FuncParam);
				if (FuncParam.Result)
					GameInstance->TimelineManager->AddNode(FuncParam.Result);
			}
		}
	}

	//赋值初始Transform
	OrgTransform = this->GetTransform();
}

// Called every frame
void AAWAoe::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if (this->IsActorBeingDestroyed())
		return;
	if (this->GetLocalRole() == ENetRole::ROLE_Authority)
	{
		if (!bPauseTick)
		{
			LivedTime += DeltaTime;
			LastTickDuration += DeltaTime;
			if (Model.TickIntervalTime > 0 && Model.TickIntervalTime <= LastTickDuration)
			{
				//Call OnTick
				if (Model.OnTick.Num())
				{
					UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
					for (int i = 0; i < Model.OnTick.Num(); i++)
					{
						UFunction* Func;
						Func = UCallFuncLib::GetUFunction(Model.OnTick[i].ClassPath, Model.OnTick[i].FunctionName);
						if (Func)
						{
							struct
							{
								AAWAoe* AOEObj;
								TArray<FString> Params;
								UTimelineNode* Result = nullptr;
							}FuncParam;
							FuncParam.AOEObj = this;
							FuncParam.Params = Model.OnTick[i].Params;
							if (Caster)
								Caster->ProcessEvent(Func, &FuncParam);
							else
								this->ProcessEvent(Func, &FuncParam);
							if (FuncParam.Result)
								GameInstance->TimelineManager->AddNode(FuncParam.Result);
							CurTickedNum++;
						}
					}
				}
				LastTickDuration = 0;
				OnAOETick();
			}
		}
		if (!bPause)
		{
			if (AOELifeSpan > 0)
			{
				if (AOELifeSpan - LivedTime <= 0)
				{
					//Call OnRemoved
					if (Model.OnRemoved.Num())
					{
						UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
						for (int i = 0; i < Model.OnRemoved.Num(); i++)
						{
							UFunction* Func;
							Func = UCallFuncLib::GetUFunction(Model.OnRemoved[i].ClassPath, Model.OnRemoved[i].FunctionName);
							if (Func)
							{
								struct
								{
									AAWAoe* AOEObj;
									TArray<FString> Params;
									UTimelineNode* Result = nullptr;
								}FuncParam;
								FuncParam.AOEObj = this;
								FuncParam.Params = Model.OnRemoved[i].Params;
								if (Caster)
									Caster->ProcessEvent(Func, &FuncParam);
								else
									this->ProcessEvent(Func, &FuncParam);
								if (FuncParam.Result)
									GameInstance->TimelineManager->AddNode(FuncParam.Result);
							}
						}
					}
					this->Destroy();
					return;
				}
			}
			//更新坐标 Call Tween
			UFunction* Func;
			Func = UCallFuncLib::GetUFunction(TweenFunc.ClassPath, TweenFunc.FunctionName);
			if (Func)
			{
				struct
				{
					float TimeElapsed;
					AAWAoe* AOEObj;
					FVector OriginPosition;
					FVector OriginDirection;
					TArray<FString> Params;
					FVector Result;
				}TweenFuncParam;
				TweenFuncParam.TimeElapsed = LivedTime;
				TweenFuncParam.AOEObj = this;
				TweenFuncParam.OriginPosition = OriginLocation;
				TweenFuncParam.OriginDirection = OriginDirection;
				TweenFuncParam.Params = TweenFunc.Params;
				if (Caster)
					Caster->ProcessEvent(Func, &TweenFuncParam);
				else
					this->ProcessEvent(Func, &TweenFuncParam);
				if (TweenFuncParam.Result != FVector(0, 0, 0))
				{
					this->SetActorLocation(TweenFuncParam.Result);
				}
			}
		}
		
		//Call OnCharacterEnter
		TArray<FBeCaughtActorInfo> EnterCharacterList = GetEnterCharacters();
		if (EnterCharacterList.Num())
		{
			if (Model.OnCharacterEnter.Num())
			{
				UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
				for (int i = 0; i < Model.OnCharacterEnter.Num(); i++)
				{
					UFunction* Func;
					Func = UCallFuncLib::GetUFunction(Model.OnCharacterEnter[i].ClassPath, Model.OnCharacterEnter[i].FunctionName);
					if (Func)
					{
						struct
						{
							AAWAoe* AOEObj;
							TArray<FBeCaughtActorInfo> EnterCharacterList;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}FuncParam;
						FuncParam.AOEObj = this;
						FuncParam.EnterCharacterList = EnterCharacterList;
						FuncParam.Params = Model.OnCharacterEnter[i].Params;
						if (Caster)
							Caster->ProcessEvent(Func, &FuncParam);
						else
							this->ProcessEvent(Func, &FuncParam);
						if (FuncParam.Result)
							GameInstance->TimelineManager->AddNode(FuncParam.Result);
					}
				}
			}
			OnCharacterEnter();
		}

		//Call OnActorEnter
		TArray<FBeCaughtActorInfo> EnterActorList = GetEnterActors();
		if (EnterActorList.Num())
		{
			if (Model.OnActorEnter.Num())
			{
				UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
				for (int i = 0; i < Model.OnActorEnter.Num(); i++)
				{
					UFunction* Func;
					Func = UCallFuncLib::GetUFunction(Model.OnActorEnter[i].ClassPath, Model.OnActorEnter[i].FunctionName);
					if (Func)
					{
						struct
						{
							AAWAoe* AOEObj;
							TArray<FBeCaughtActorInfo> EnterActors;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}FuncParam;
						FuncParam.AOEObj = this;
						FuncParam.EnterActors = EnterActorList;
						FuncParam.Params = Model.OnActorEnter[i].Params;
						if (Caster)
							Caster->ProcessEvent(Func, &FuncParam);
						else
							this->ProcessEvent(Func, &FuncParam);
						if (FuncParam.Result)
							GameInstance->TimelineManager->AddNode(FuncParam.Result);
					}
				}
			}
			OnActorEnter();
		}
		
		//Call OnCharacterLeave
		TArray<AAwCharacter*> LeaveCharacterList = GetLeavingCharacters();
		if (LeaveCharacterList.Num())
		{
			if (Model.OnCharacterLeave.Num())
			{
				UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
				for (int i = 0; i < Model.OnCharacterLeave.Num(); i++)
				{
					UFunction* Func;
					Func = UCallFuncLib::GetUFunction(Model.OnCharacterLeave[i].ClassPath, Model.OnCharacterLeave[i].FunctionName);
					if (Func)
					{
						struct
						{
							AAWAoe* AOEObj;
							TArray<AAwCharacter*> LeaveCharacterList;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}FuncParam;
						FuncParam.AOEObj = this;
						FuncParam.LeaveCharacterList = LeaveCharacterList;
						FuncParam.Params = Model.OnCharacterLeave[i].Params;
						if (Caster)
							Caster->ProcessEvent(Func, &FuncParam);
						else
							this->ProcessEvent(Func, &FuncParam);
						if (FuncParam.Result)
							GameInstance->TimelineManager->AddNode(FuncParam.Result);
					}
				}
			}
			OnCharacterLeave();
		}

		//Call OnActortLeave
		TArray<AActor*> LeaveActorList = GetLeavingActors();
		if (LeaveActorList.Num())
		{
			if (Model.OnActorLeave.Num())
			{
				UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
				for (int i = 0; i < Model.OnActorLeave.Num(); i++)
				{
					UFunction* Func;
					Func = UCallFuncLib::GetUFunction(Model.OnActorLeave[i].ClassPath, Model.OnActorLeave[i].FunctionName);
					if (Func)
					{
						struct
						{
							AAWAoe* AOEObj;
							TArray<AActor*> LeaveActors;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}FuncParam;
						FuncParam.AOEObj = this;
						FuncParam.LeaveActors = LeaveActorList;
						FuncParam.Params = Model.OnActorLeave[i].Params;
						if (Caster)
							Caster->ProcessEvent(Func, &FuncParam);
						else
							this->ProcessEvent(Func, &FuncParam);
						if (FuncParam.Result)
							GameInstance->TimelineManager->AddNode(FuncParam.Result);
					}
				}
			}
			OnActorLeave();
		}

		AttackHitManager->Update(DeltaTime);
	}
}

void AAWAoe::Pause(bool PauseTick)
{
	bPause = true;
	bPauseTick = PauseTick;
}

void AAWAoe::Resume()
{
	bPause = bPauseTick = false;
}

TArray<FBeCaughtActorInfo> AAWAoe::CharacterInRange(bool IncludeEnterCha, bool IncludeLeavingCha)
{
	return GetCaughtCharacters(IncludeEnterCha, IncludeLeavingCha);
}

TArray<FBeCaughtActorInfo> AAWAoe::GetEnterCharacters()
{
	TArray<FBeCaughtActorInfo> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, true, false))
	{
		AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
		//ECS怪物也加入判断（但不会被使用）
		bool HaveCharacter = Character || ActorInfo.BeCaughtSubject.IsValid();
		if (HaveCharacter && ActorInfo.CaughtState == EActorCaughtState::Enter)
		{
			CaughtList.Add(ActorInfo);
		}
	}
	return CaughtList;
}

TArray<FBeCaughtActorInfo> AAWAoe::GetCaughtCharacters(bool IncludeEnterCha, bool IncludeLeavingCha)
{
	// TArray<FBeCaughtActorInfo> CaughtList;
	// for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, IncludeEnterCha, IncludeLeavingCha))
	// {
	// 	AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
	// 	// if (Character)
	// 	{
	// 		CaughtList.Add(ActorInfo);
	// 	}
	// }
	return AttackHitManager->ThisTickValidTarget(true, IncludeEnterCha, IncludeLeavingCha);
}

TArray<FVector> AAWAoe::GetVectors(TArray<FBeCaughtActorInfo> Targets, bool IgnoreDead, bool FriendlyFire, bool Repeat, int Num)
{
	TArray<FVector> Positions;

	// 收集所有有效的位置
	for (const FBeCaughtActorInfo& TargetInfo : Targets)
	{
		FVector TargetPosition = FVector::ZeroVector;

		// 处理普通角色
		if (AAwCharacter* Character = Cast<AAwCharacter>(TargetInfo.BeCaughtActor))
		{
			if (!IsValid(Character) || (IgnoreDead && Character->Dead(true)))
				continue;

			// 检查友军伤害设置
			if (Caster && !FriendlyFire && !Caster->IsEnemy(Character))
				continue;

			TargetPosition = Character->GetActorLocation();
		}
		// 处理ECS怪物
		else if (TargetInfo.BeCaughtSubject.IsValid())
		{
			// ECS怪物通常是敌人，总是包含
			const auto Located = TargetInfo.BeCaughtSubject.GetTraitPtr<FLocated, EParadigm::Unsafe>();
			if (Located)
			{
				TargetPosition = Located->Location;
			}
		}

		// 添加有效位置
		if (!TargetPosition.IsZero())
		{
			Positions.Add(TargetPosition);
		}
	}

	TArray<FVector> Result;
	// 根据Num参数处理结果
	if (Num > 0 && Positions.Num() > 0)
	{
		int repeats = Num / Positions.Num();
		int rest = Num % Positions.Num();
		if (Repeat)
		{
			for (int i =0;i<repeats;i++)
			{
				Result.Append(Positions);
			}
		}
		if (rest>0)
		{
			// Num不足时随机选择，不重复
			TArray<int32> AvailableIndices;

			// 创建索引数组
			for (int32 i = 0; i < Positions.Num(); i++)
			{
				AvailableIndices.Add(i);
			}

			// 随机选择Num个不重复的位置
			for (int32 i = 0; i < Num; i++)
			{
				int32 RandomIndex = FMath::RandRange(0, AvailableIndices.Num() - 1);
				int32 SelectedIndex = AvailableIndices[RandomIndex];
				Result.Add(Positions[SelectedIndex]);
				AvailableIndices.RemoveAt(RandomIndex);
			}
		}
		if (Result.Num()>0)
		{
			return Result;
		}
	}

	return Positions;
}

TArray<FBeCaughtActorInfo> AAWAoe::GetCaughtCharactersSortByLoc(bool IncludeEnterCha , bool IncludeLeavingCha )
{
	TArray<FBeCaughtActorInfo> Result = AttackHitManager->ThisTickValidTarget(true, IncludeEnterCha, IncludeLeavingCha);
	
	FBeCaughtActorInfo tmp = FBeCaughtActorInfo();

	TMap<float,FBeCaughtActorInfo> Check;
	int i, j;
	for(i = 1; i < Result.Num(); i++)
	{ 
		j = i;
		tmp = Result[i];
		if (!IsValid(Result[j-1].BeCaughtActor))
		{
			continue;
		}
		bool bLoop =  Result[j-1].BeCaughtActor->GetDistanceTo(this)>tmp.BeCaughtActor->GetDistanceTo(this);
		Check.Add(Result[i].BeCaughtActor->GetDistanceTo(this)>tmp.BeCaughtActor->GetDistanceTo(this),Result[i]);
		while(j &&bLoop)
		{ 
			Result[j] = Result[j - 1];  
			j--;
		}
		Result[j] = tmp;
	}
	
	return Result;
}

TArray<AAwCharacter*> AAWAoe::GetLeavingCharacters()
{
	TArray<AAwCharacter*> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, false, true))
	{
		AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
		//ECS怪物也加入判断（但不会被使用）
		bool HaveCharacter = Character || ActorInfo.BeCaughtSubject.IsValid();
		if (HaveCharacter && ActorInfo.CaughtState == EActorCaughtState::Leave)
		{
			CaughtList.Add(Character);
		}
	}
	return CaughtList;
}

TArray<FBeCaughtActorInfo> AAWAoe::GetEnterActors()
{
	TArray<FBeCaughtActorInfo> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, true, false))
	{
		AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
		if (!Character && ActorInfo.CaughtState == EActorCaughtState::Enter)
		{
			CaughtList.Add(ActorInfo);
		}
	}
	return CaughtList;
}

TArray<FBeCaughtActorInfo> AAWAoe::GetCatchedActors()
{
	TArray<FBeCaughtActorInfo> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, false, false))
	{
		AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
		if (!Character)
		{
			CaughtList.Add(ActorInfo);
		}
	}
	return CaughtList;
}

TArray<AActor*> AAWAoe::GetLeavingActors()
{
	TArray<AActor*> CaughtList;
	for (FBeCaughtActorInfo ActorInfo : AttackHitManager->ThisTickValidTarget(true, false, true))
	{
		AAwCharacter* Character = Cast<AAwCharacter>(ActorInfo.BeCaughtActor);
		if (!Character && ActorInfo.CaughtState == EActorCaughtState::Leave)
		{
			CaughtList.Add(ActorInfo.BeCaughtActor);
		}
	}
	return CaughtList;
}

bool AAWAoe::HaveEnemy(TArray<FBeCaughtActorInfo> Caughts)
{
	// 如果没有施法者，无法判断敌对关系
	if (!Caster)
		return false;

	// 遍历所有被捕获的对象
	for (const FBeCaughtActorInfo& CaughtInfo : Caughts)
	{
		// 检查普通角色
		if (AAwCharacter* Character = Cast<AAwCharacter>(CaughtInfo.BeCaughtActor))
		{
			// 检查角色是否有效且未死亡，并且是敌人
			if (IsValid(Character) && !Character->Dead(true) && Caster->IsEnemy(Character))
			{
				return true;
			}
		}
		// 检查ECS怪物
		else if (CaughtInfo.BeCaughtSubject.IsValid())
		{
			// ECS怪物默认被视为敌人（基于现有代码模式）
			return true;
		}
	}

	return false;
}

void AAWAoe::Destroyed()
{
	Super::Destroyed();
}

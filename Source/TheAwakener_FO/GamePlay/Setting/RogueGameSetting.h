// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DLSSLibrary.h"
#include "Engine/Engine.h"
#include "XeSSBlueprintLibrary.h"
#include "GameFramework/GameUserSettings.h"
#include "RogueGameSetting.generated.h"

//语言枚举
UENUM(BlueprintType)
enum class ELanguage : uint8
{
	English,
	Chinese,
	Korean,
	Japanese
};

UENUM(BlueprintType)
enum class EGamepadIconType : uint8
{
	XBox,
	PlayStation,
	Switch
};

UCLASS()
class THEAWAKENER_FO_API URogueGameSetting : public UGameUserSettings
{
	GENERATED_BODY()

	URogueGameSetting();

public:

	UFUNCTION(BlueprintPure, Category=Settings)
	static URogueGameSetting* GetRogueGameSettings()
	{
		return Cast<URogueGameSetting>(GEngine->GetGameUserSettings());
	}

	UFUNCTION(BlueprintCallable)
	virtual void LoadRogueSettings(bool bForceReload) ;
	UFUNCTION(BlueprintCallable)
	virtual void ApplyRogueSettings(bool bCheckForCommandLineOverrides) ;

	UFUNCTION(BlueprintCallable)
	virtual void ApplyRogueVedioSettings(bool bCheckForCommandLineOverrides) ;
	UFUNCTION(BlueprintCallable)
	virtual void ApplyRogueGameSettings(bool bCheckForCommandLineOverrides) ;
	UFUNCTION(BlueprintCallable)
	virtual void ApplyRogueKeySettings(bool bCheckForCommandLineOverrides) ;
	UFUNCTION(BlueprintCallable)
	virtual void ApplyRogueVolumSettings(bool bCheckForCommandLineOverrides) ;
	
	UFUNCTION(BlueprintCallable)
	virtual  void SetToVedioDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetToVolumeDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetToLanguageDefaults();
	UFUNCTION(BlueprintCallable)
	virtual  void SetToGamepadIconTypeDefaults();
	
	UFUNCTION(BlueprintCallable)
	virtual  void SetToMouseXSensitivityDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetToMouseYSensitivityDefaults();

	UFUNCTION(BlueprintCallable)
	virtual  void SetToShowDamageTextDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetToAimAssistDefaults();

	UFUNCTION(BlueprintCallable)
	virtual void SetToGamepadFeedBackDefaults();
	
	UFUNCTION(BlueprintCallable)
	virtual  void SetToGamepadXSensitivityDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetToGamepadYSensitivityDefaults();

	UFUNCTION(BlueprintCallable)
	virtual  void SetTobIsInvertX_AxisDefaults() ;
	UFUNCTION(BlueprintCallable)
	virtual  void SetTobIsInvertY_AxisDefaults();
	
	UFUNCTION(BlueprintCallable)
	virtual void K2_RequestUIUpdate();
//New Setting Parameters

	//---------------------------画面设置----------------------------------
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetDisplayGamma() const;
	UFUNCTION(BlueprintCallable)
	void SetDisplayGamma(float InGamma);

	UFUNCTION(BlueprintCallable, Category=Settings)
	int32 GetGraphicsQuality() const;

	UFUNCTION(BlueprintCallable, Category=Settings)
	void SetGraphicsQuality(int32 Value);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	EXeSSQualityMode GetXESSMode() const;
	
	UFUNCTION(BlueprintCallable)
	void SetXESSMode(EXeSSQualityMode NewMode);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	UDLSSMode GetDLSSMode() const;
	UFUNCTION(BlueprintCallable)
	void SetDLSSMode(UDLSSMode NewMode);
	
	//---------------------------声音设置----------------------------------

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMainVolume() const;
	UFUNCTION(BlueprintCallable)
	void SetMainVolume(float InMainVolume);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetBGMVolume() const;
	UFUNCTION(BlueprintCallable)
	void SetBGMVolume(float InBGMVolume);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetSFXVolume() const;
	UFUNCTION(BlueprintCallable)
	void SetSFXVolume(float InSFXVolume);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetVoiceVolume() const;
	UFUNCTION(BlueprintCallable)
	void SetVoiceVolume(float InVoiceVolume);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetUISoundVolume() const;
	UFUNCTION(BlueprintCallable)
	void SetUISoundVolume(float InUISoundVolume);
	
	//---------------------------游戏设置----------------------------------
	//语言设置
	UFUNCTION(BlueprintCallable,BlueprintPure)
	ELanguage GetLanguage() const;
	UFUNCTION(BlueprintCallable)
	void SetLanguage(ELanguage InLanguage);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	EGamepadIconType GetGamepadIconType() const;
	UFUNCTION(BlueprintCallable)
	void SetGamepadIconType(EGamepadIconType InGamepadIconType);
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetShowDamageText() const;
	UFUNCTION(BlueprintCallable)
	void SetShowDamageText(bool OnOrOff);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetAimAssist() const;
	UFUNCTION(BlueprintCallable)
	void SetAimAssist(bool OnOrOff);

	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetGamepadFeedBack() const;
	//内部做了Clamp
	UFUNCTION(BlueprintCallable)
	void SetGamepadFeedBack(bool OnOrOff);
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMouseXSensitivity() const;
	//内部做了Clamp
	UFUNCTION(BlueprintCallable)
	void SetMouseXSensitivity(float Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMaxMouseXSensitivity() const;
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMinMouseXSensitivity() const;

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMouseYSensitivity() const;
	//内部做了Clamp
	UFUNCTION(BlueprintCallable)
	void SetMouseYSensitivity(float Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMaxMouseYSensitivity() const;
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMinMouseYSensitivity() const;
	

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetGamepadXSensitivity() const;
	//内部做了Clamp
	UFUNCTION(BlueprintCallable)
	void SetGamepadXSensitivity(float Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMaxGamepadXSensitivity() const;
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMinGamepadXSensitivity() const;

	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetGamepadYSensitivity() const;
	//内部做了Clamp
	UFUNCTION(BlueprintCallable)
	void SetGamepadYSensitivity(float Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMaxGamepadYSensitivity() const;
	UFUNCTION(BlueprintCallable,BlueprintPure)
	float GetMinGamepadYSensitivity() const;

	UFUNCTION(BlueprintCallable)
	void SetbIsInvertX_Axis(bool Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetbIsInvertX_Axis();

	UFUNCTION(BlueprintCallable)
	void SetbIsInvertY_Axis(bool Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetbIsInvertY_Axis();

	UFUNCTION(BlueprintPure)
	TMap<FString, FString> GetCustomKey_Keyboard() { return CustomKey_Keyboard; }
	UFUNCTION(BlueprintCallable)
	void SetCustomKey_Keyboard(FString ActionKeyMap, FString ActionKey);
	UFUNCTION(BlueprintCallable)
	void EmptyCustomKey_Keyboard();
	UFUNCTION(BlueprintPure)
	TMap<FString, FString> GetCustomKey_Gamepad() { return CustomKey_Gamepad; }
	UFUNCTION(BlueprintCallable)
	void SetCustomKey_Gamepad(FString ActionKeyMap, FString ActionKey);
	UFUNCTION(BlueprintCallable)
	void EmptyCustomKey_Gamepad();

	UFUNCTION(BlueprintCallable)
	void SetCustomKeyToTempKey();
	UFUNCTION(BlueprintCallable)
	void SetTempKeyToCustomKey();
	UFUNCTION(BlueprintPure)
	TMap<FString, FString> GetTempKey_Keyboard() { return TempKey_Keyboard; }
	UFUNCTION(BlueprintCallable)
	void SetTempKey_Keyboard(FString ActionKeyMap, FString ActionKey);
	UFUNCTION(BlueprintCallable)
	void EmptyTempKey_Keyboard();
	UFUNCTION(BlueprintPure)
	TMap<FString, FString> GetTempKey_Gamepad() { return TempKey_Gamepad; }
	UFUNCTION(BlueprintCallable)
	void SetTempKey_Gamepad(FString ActionKeyMap, FString ActionKey);
	UFUNCTION(BlueprintCallable)
	void EmptyTempKey_Gamepad();

	//---------------------------额外设置----------------------------------
	UFUNCTION(BlueprintCallable)
	void SetSwapsArtifactAction(bool Value);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	bool GetSwapsArtifactAction(){return bSwapsArtifactAction;};

protected:
	
	//---------------------------按键设置----------------------------------
	// ActionCmd - ActionKey
	UPROPERTY(Config)
	TMap<FString, FString> CustomKey_Keyboard;
	// ActionCmd - ActionKey
	UPROPERTY(Config)
	TMap<FString, FString> CustomKey_Gamepad;
	
	UPROPERTY()
	TMap<FString, FString> TempKey_Keyboard;
	UPROPERTY()
	TMap<FString, FString> TempKey_Gamepad;
	
	//---------------------------画面设置----------------------------------
	//画面亮度 伽马值
	void ApplyDisplayGamma();
	UPROPERTY(Config)
	float DisplayGamma = 2.2;
	UPROPERTY(Config)
	float DefaultDisplayGamma = 2.2;
	//画面质量
	void ApplyGraphicsQuality();
	UPROPERTY(Config)
	int32 GraphicsQuality = 3;
	UPROPERTY(Config)
	int32 DefaultGraphicsQuality = 3;

	UPROPERTY(Config)
	bool DefaultUseVSync = false;
	
	//是否使用 inter  超分辨率策略
	void ApplyXESSMode();
	UPROPERTY(Config)
	EXeSSQualityMode XESSMode = EXeSSQualityMode::Off;
	EXeSSQualityMode DefaultXESSMode = EXeSSQualityMode::Off;

	//是否使用 inter  超分辨率策略
	void ApplyDLSSMode();
	UPROPERTY(Config)
	UDLSSMode DLSSMode = UDLSSMode::Balanced;
	UDLSSMode DefaultDLSSMode =UDLSSMode::Performance;
	
	//---------------------------声音设置----------------------------------
	
	void ApplyMainVolume();
	UPROPERTY(Config)
	float MainVolume = 0.7;
	UPROPERTY(Config)
	float DefaultMainVolume = 0.7;

	void ApplyBGMVolume();
	UPROPERTY(Config)
	float BGMVolume = 0.5;
	UPROPERTY(Config)
	float DefaultBGMVolume = 0.5;

	void ApplySFXVolume();
	UPROPERTY(Config)
	float SFXVolume = 0.5;
	UPROPERTY(Config)
	float DefaultSFXVolume = 0.5;

	void ApplyVoiceVolume();
	UPROPERTY(Config)
	float VoiceVolume = 0.5;
	UPROPERTY(Config)
	float DefaultVoiceVolume = 0.5;

	void ApplyUISoundVolume();
	UPROPERTY(Config)
	float UISoundVolume = 0.5;
	UPROPERTY(Config)
	float DefaultUISoundVolume = 0.5;

	//---------------------------游戏设置----------------------------------
	//语言设置
	void ApplyLanguage();
	UPROPERTY(Config)
	ELanguage Language = ELanguage::English;
	
	ELanguage DefaultLanguage = ELanguage::English;
	
	UPROPERTY(Config)
	EGamepadIconType GamepadIconType = EGamepadIconType::XBox;
	EGamepadIconType DefaultGamepadIconType = EGamepadIconType::XBox;
	
	//伤害数字显示设置
	UPROPERTY(Config)
	bool ShowDamageText = true;
	UPROPERTY(Config)
	bool DefaultShowDamageText = true;


	//辅助瞄准设置
	UPROPERTY(Config)
	bool AimAssist = true;
	UPROPERTY(Config)
	bool DefaultAimAssist = true;

	//手柄振动强度
	UPROPERTY(Config)
	bool GamepadFeedBack = true;
	bool DefaultGamepadFeedBack = true;
	
	//鼠标X轴灵敏度
	UPROPERTY(Config)
	float MouseXSensitivity = 1;
	
	float DefaultMouseXSensitivity = 1;
	float MaxMouseXSensitivity = 10;
	float MinMouseXSensitivity = 0.1;

	//鼠标Y轴灵敏度
	UPROPERTY(Config)
	float MouseYSensitivity = 1;
	
	float DefaultMouseYSensitivity = 1;
	float MaxMouseYSensitivity = 10;
	float MinMouseYSensitivity = 0.1;

	
	//手柄X灵敏度
	UPROPERTY(Config)
	float GamepadXSensitivity = 1;
	
	float DefaultGamepadXSensitivity = 1;
	float MaxGamepadXSensitivity = 10;
	float MinGamepadXSensitivity = 0.1;

	//手柄Y灵敏度
	UPROPERTY(Config)
	float GamepadYSensitivity = 1;
	
	float DefaultGamepadYSensitivity = 1;
	float MaxGamepadYSensitivity = 10;
	float MinGamepadYSensitivity = 0.1;

	//视角X轴反转
	UPROPERTY(Config)
	bool bIsInvertX_Axis = false;
	bool bDefaultIsInvertX_Axis = false;

	//视角Y轴反转
	UPROPERTY(Config)
	bool bIsInvertY_Axis = false;
	bool bDefaultIsInvertY_Axis = false;

	//---------------------------额外设置----------------------------------
	//双法器操作按键互换
	UPROPERTY(Config)
	bool bSwapsArtifactAction = false;
};
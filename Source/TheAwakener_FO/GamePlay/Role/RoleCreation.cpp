// Fill out your copyright notice in the Description page of Project Settings.


#include "RoleCreation.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FRoleType FRoleType::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRoleType Res;

	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.Sex = UDataFuncLib::AwGetStringField(JsonObj, "Sex");
	Res.Desc = UDataFuncLib::AwGetStringField(JsonObj, "Desc");
	Res.BpPath = UDataFuncLib::AwGetStringField(JsonObj, "BpPath");
	Res.FolderName = UDataFuncLib::AwGetStringField(JsonObj, "FolderName");

	Res.Equipments = UDataFuncLib::AwGetStringArrayField(<PERSON>sonObj, "Equipments");
	Res.MainHandId = UDataFuncLib::AwGetStringField(JsonObj, "MainHand");
	Res.OffHandId = UDataFuncLib::AwGetStringField(JsonObj, "OffHand");
	Res.Attack = UDataFuncLib::AwGetNumberField(JsonObj, "PAtk", 1);
	Res.HP = UDataFuncLib::AwGetNumberField(JsonObj, "HP", 1);
	Res.BreakRatio = UDataFuncLib::AwGetNumberField(JsonObj, "BreakRatio", 1);
	Res.DefaultBattleStyle = UDataFuncLib::AwGetStringField(JsonObj, "DefaultBattleStyle", "");
	
	return Res;
}

FRoleCreation FRoleCreation::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRoleCreation Res;

	if (JsonObj->HasField("Types"))
		for (const TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "Types"))
			Res.Types.Add(FRoleType::FromJson(JsonValue->AsObject()));

	if (JsonObj->HasField("RoguePawns"))
		for (const TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj, "RoguePawns"))
			Res.RoguePawns.Add(FRoleType::FromJson(JsonValue->AsObject()));
	
	return Res;
}

// Fill out your copyright notice in the Description page of Project Settings.


#include "ActorCatcher.h"

#include "BubbleCage.h"
#include "SubjectiveActorComponent.h"
#include "TimerManager.h"
#include "GenericPlatform/GenericPlatformCrashContext.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/MathFuncLib.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/ECSDamageLib.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/MechanicRunner.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/Attacks.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/Enemy.h"
#include "TheAwakener_FO/GamePlay/ApparatusCustom/Traits/TrCommand.h"
#include "TheAwakener_FO/GamePlay/AssetUserData/AttackHitBoxSign.h"
#include "TheAwakener_FO/GamePlay/Bullet/AwBullet.h"


UAttackHitManager::UAttackHitManager()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UAttackHitManager::BindAttackBoxECS(const int32& BoxKey,const FSubjectHandle& Subject,const FTrAttackBox& AttackBox)
{
	AttackBoxEntity.Add(BoxKey,Subject);
	FLocated Located;
	if (AttackBoxes.Contains(BoxKey))
	{
		Located.Location=AttackBoxes[BoxKey]->GetComponentLocation();
	}
	Subject->SetTrait(AttackBox);
	Subject->SetTrait(Located);
	
}

void UAttackHitManager::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	if (!Started) return;
	Update(DeltaTime);
}


void UAttackHitManager::Update(float DeltaTime)
{
	//所有Records得减去持续时间，即使时间到了也不能清除掉，因为还有次数记录的
	for (int i = 0; i < this->HitRecords.Num(); i++)
	{
		HitRecords[i].Duration -= DeltaTime;
	}

	//对于所有抓住的角色进行再判断，由于OnOverlapEnd只是把角色标记为即将离开，所以这里要清除掉即将离开的家伙
	int i = 0;
	while (i  < Actors.Num())
	{
		if (Actors[i].CaughtState == EActorCaughtState::Leave)
		{
			if (Actors[i].DelayDuration > 0)
			{
				Actors[i].DelayDuration -= DeltaTime;
				i++;
			}else
			{
				Actors.RemoveAt(i);
			}
		}else
		{
			if (Actors[i].CaughtState == EActorCaughtState::Enter && LivingAttackBoxes.Contains(FStringPool::Register(Actors[i].AttackBoxName)))
			{
				Actors[i].CaughtState = EActorCaughtState::Caught;
			}
			i ++;
		}
	}
	auto AtkBoxKeys = GetAllActiveAttackBoxKeys();
	for (const auto Key : AtkBoxKeys)
	{
		SyncECSAttackBoxPosition(Key);
	}

	//使用ABubbleCage::GetOverlapping检测ECS怪物命中
	CheckECSMonsterHit();
}

void UAttackHitManager::AddAttackHitBox(int32 BoxKey, UPrimitiveComponent* Box)
{
	if (!Box || this->AttackBoxes.Contains(BoxKey)) return;
	this->AttackBoxes.Add(BoxKey, Box);
	Box->OnComponentBeginOverlap.AddDynamic(this, &UAttackHitManager::OnOverlapBegin);
	Box->OnComponentEndOverlap.AddDynamic(this, &UAttackHitManager::OnOverlapEnd);
	
}

void UAttackHitManager::ClearAllHitRecords()
{
	this->HitRecords.Empty();
}

// void UAttackHitManager::SetCaughtActorAsLeaving(const AActor* Actor, const UBeHitBoxBase* BeHitBox)
// {
// 	if (!Actor) return;
// 	for (int i = 0; i < Actors.Num(); i++)
// 	{
// 		if (Actors[i].BeCaughtActor == Actor && (!BeHitBox || BeHitBox == Actors[i].BeHitBox))
// 		{
// 			Actors[i].CaughtState = EActorCaughtState::Leave;
// 		}
// 	}
// }

TArray<FBeCaughtActorInfo> UAttackHitManager::EnterActors()
{
	TArray<FBeCaughtActorInfo> Res;
	for (FBeCaughtActorInfo AInfo : this->Actors)
	{
		if (AInfo.CaughtState == EActorCaughtState::Enter)
		{
			Res.Add(AInfo);
		}
	}
	return Res;
}
TArray<FBeCaughtActorInfo> UAttackHitManager::CaughtActors()
{
	TArray<FBeCaughtActorInfo> Res;
	for (FBeCaughtActorInfo AInfo : this->Actors)
	{
		if (AInfo.CaughtState == EActorCaughtState::Caught)
		{
			Res.Add(AInfo);
		}
	}
	return Res;
}
TArray<FBeCaughtActorInfo>UAttackHitManager::LeavingActors()
{
	TArray<FBeCaughtActorInfo> Res;
	for (FBeCaughtActorInfo AInfo : this->Actors)
	{
		if (AInfo.CaughtState == EActorCaughtState::Leave)
		{
			Res.Add(AInfo);
		}
	}
	return Res;
}

void UAttackHitManager::AddHitRecord(FOffenseHitRecord Record)
{
	for (int i = 0; i < this->HitRecords.Num(); i++)
	{
		if (this->HitRecords[i].SourceId == Record.SourceId && this->HitRecords[i].Target == Record.Target && this->HitRecords[i].Index == Record.Index)
		{
			this->HitRecords[i].Duration = Record.Duration;
			this->HitRecords[i].CanHits -= 1;
			return;
		}
	}
	this->HitRecords.Add(Record);
}

// void UAttackHitManager::AddActor(FString AttackBoxName, AActor* Actor, UBeHitBoxBase* BeHitBox, float LeavingDelay)
// {
// 	
// 	for (const FBeCaughtActorInfo AInfo : Actors)
// 	{
// 		if (AInfo.BeCaughtActor == Actor && AInfo.BeHitBox == BeHitBox && AInfo.AttackBoxName == AttackBoxName) return;
// 	} 
// 	const FBeCaughtActorInfo AInfo = FBeCaughtActorInfo(AttackBoxName, Actor, BeHitBox, LeavingDelay);
// 	Actors.Add(AInfo);
// 	//UKismetSystemLibrary::PrintString(this, FString("CatcherAddOverlap"));
// }

void UAttackHitManager::AddActor(FString AttackBoxName, AActor* Actor, USceneComponent* CaughtBox, float LeavingDelay)
{
	bool AsEnter = true;
	for ( const FBeCaughtActorInfo AInfo : Actors)
	{
		if (AInfo.BeCaughtActor == Actor&&AInfo.CaughtState==EActorCaughtState::Caught&&!bSameActorEnter)
		{
			AsEnter = false;
		}
		if (
			AInfo.BeCaughtActor == Actor &&
			AInfo.CaughtHitBoxComponent == CaughtBox &&
			AInfo.AttackBoxName == AttackBoxName
		)
			return;	//找到“完全相同”的了，就不能添加了
	}

	 FBeCaughtActorInfo AInfo = FBeCaughtActorInfo(AttackBoxName, Actor, CaughtBox, LeavingDelay);
	if (AsEnter == false)
	{
		AInfo.CaughtState = EActorCaughtState::Caught;
	}
	Actors.Add(AInfo);
}

void UAttackHitManager::AddECSMonster(FString AttackBoxName, FSubjectHandle Monster,float LeavingDelay)
{
	if (!Monster.IsValid()) return;

	bool AsEnter = true;
	for (const FBeCaughtActorInfo AInfo : Actors)
	{
		// 检查是否是ECS怪物（BeCaughtActor为nullptr，BeCaughtSubject有效）
		if (AInfo.BeCaughtActor == nullptr && AInfo.BeCaughtSubject.IsValid())
		{
			if (AInfo.BeCaughtSubject == Monster && AInfo.CaughtState == EActorCaughtState::Caught && !bSameActorEnter)
			{
				AsEnter = false;
			}
			if (AInfo.BeCaughtSubject == Monster && AInfo.AttackBoxName == AttackBoxName)
			{
				return;	//找到"完全相同"的了，就不能添加了
			}
		}
	}

	// 使用ECS怪物专用构造函数创建FBeCaughtActorInfo
	FBeCaughtActorInfo AInfo = FBeCaughtActorInfo(AttackBoxName, Monster,LeavingDelay);
	AInfo.AttackBoxSubject = AttackBoxEntity[FStringPool::Register(AttackBoxName)];
	if (AsEnter == false)
	{
		AInfo.CaughtState = EActorCaughtState::Caught;
	}
	Actors.Add(AInfo);
}

// void UAttackHitManager::RemoveActorByAttackBoxName(FString AttackBoxName, AActor* Actor, UBeHitBoxBase* BeHitBox)
// {
// 	for (int i = 0; i < this->Actors.Num(); i++)
// 	{
// 		if (Actors[i].AttackBoxName == AttackBoxName)
// 		{
// 			if (Actors[i].BeCaughtActor == Actor && Actors[i].BeHitBox == BeHitBox)
// 			{
// 				Actors[i].CaughtState = EActorCaughtState::Leave;
// 				//UKismetSystemLibrary::PrintString(this, FString("CatcherRemoveOverlap"));
// 			}
// 		}
// 	}
// }

void UAttackHitManager::RemoveActorByAttackBoxName(FString AttackBoxName, AActor* Actor, USceneComponent* CaughtBox)
{
	for (int i = 0; i < this->Actors.Num(); i++)
	{
		if (Actors[i].AttackBoxName == AttackBoxName)
		{
			if (Actors[i].BeCaughtActor == Actor && Actors[i].CaughtHitBoxComponent == CaughtBox)
			{
				Actors[i].CaughtState = EActorCaughtState::Leave;
			}
		}
	}
}

void UAttackHitManager::BeginPlay()
{
	Super::BeginPlay();
	InitAttackBox();
}

void UAttackHitManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// 解绑所有AttackBox的Overlap事件，防止悬空指针调用
	for (auto& BoxPair : AttackBoxes)
	{
		if (IsValid(BoxPair.Value))
		{
			BoxPair.Value->OnComponentBeginOverlap.RemoveDynamic(this, &UAttackHitManager::OnOverlapBegin);
			BoxPair.Value->OnComponentEndOverlap.RemoveDynamic(this, &UAttackHitManager::OnOverlapEnd);
		}
	}

	// 销毁所有AttackBoxEntity
	for (auto& EntityPair : AttackBoxEntity)
	{
		if (EntityPair.Value.IsValid())
		{
			EntityPair.Value.DespawnDeferred();
		}
	}

	// 清空映射表
	AttackBoxEntity.Empty();
	AttackBoxes.Empty();

	Super::EndPlay(EndPlayReason);
}

void UAttackHitManager::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	const UAttackHitBoxSign* BoxSign = OverlappedComponent->GetAssetUserData<UAttackHitBoxSign>();
	//UBeHitBoxBase* HitBox = Cast<UBeHitBoxBase>(OtherComp);
	const UBeCaughtHitBox* HitBox = OtherComp->GetAssetUserData<UBeCaughtHitBox>();
	if (OverlappedComponent->GetOwner() == OtherActor) return;
	
	// UKismetSystemLibrary::PrintString(this->GetOwner(),  this->GetOwner()->GetName().Append(FString("Overlap:").Append(OtherComp->GetOwner()->GetName())),
	// 	true, true, FLinearColor::Green, 20);
	if (!BoxSign || !HitBox) return;
	if (!HitBox->Active)
	{
		return;
	}
	USceneComponent* SComp = Cast<USceneComponent>(OtherComp);
	this->AddActor(BoxSign->Name, OtherActor, SComp, BoxSign->LeavingDelay);
}

void UAttackHitManager::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	const UAttackHitBoxSign* BoxSign = OverlappedComponent->GetAssetUserData<UAttackHitBoxSign>();
	if (OverlappedComponent->GetOwner() == OtherActor) return;
	if (!BoxSign) return;
	RemoveActorByAttackBoxName(BoxSign->Name, OtherActor,  Cast<USceneComponent>(OtherComp));
}

void UAttackHitManager::SetupECSCollision(AAwCharacter* Char,UActorComponent* HitBox,UPrimitiveComponent* Comp,AMechanism* Mechanism,int32 BoxNameKey)
{
	//只处理玩家的
	if (!Char->IsPlayerCharacter())
	{
		return;
	}
	//角色身上的攻击盒
	FSubjectHandle Subject = Mechanism->SpawnSubject();
	FTrAttackBox Attackbox;
	if (AttackBoxType==Melee)
	{
		UAttackHitBox* AtkBox = Cast<UAttackHitBox>(HitBox);
		Attackbox.Radius = FMath::Max(AtkBox->GetScaledCapsuleRadius(),AtkBox->GetScaledCapsuleHalfHeight()),
		Attackbox.HitBox = AtkBox;
	}
	Attackbox.BoxType = AttackBoxType;
	Attackbox.Attacker = Char;

	//子弹上的攻击盒
	if (AttackBoxType == Bullet)
	{
		auto Bullet = Cast<AAwBullet>(GetOwner());
		Attackbox.Attacker = Bullet->Caster;
		Attackbox.Bullet = Bullet;
		Attackbox.BulletModelKey = FBulletModelPool::Register(Bullet->Model);
	}
	if (AttackBoxType != Melee)
	{
		Attackbox.Radius = Comp->Bounds.SphereRadius;
	}
	BindAttackBoxECS(BoxNameKey,Subject,Attackbox);
	if (AttackBoxType!=Melee)
	{
		//子弹的盒子貌似没有开关过程，只能手动保持激活了
		auto flag = UFlagmarkBitPool::Acquire();
		ActiveECSAttack(FStringPool::Get(BoxNameKey), flag);
		ActiveAttackHitBox(FStringPool::Get(BoxNameKey));
	}
}

bool UAttackHitManager::TargetCanBeHitByHitRecord(AActor* Target, FString AttackSourceId, int Index)
{
	if (Target == nullptr) return false;
	for (const FOffenseHitRecord Rec : this->HitRecords)
	{
		if (Rec.Target == Target && Rec.SourceId == AttackSourceId && Rec.Index == Index && (Rec.Duration > 0 || Rec.CanHits <= 0)) return false;
	}
	return true;
}

TArray<FOffendedCaughtResult> UAttackHitManager::GetThisTickTargetCaughtInfo(AActor* Target, TArray<FOffenseInfo> WithOffending, bool IncludeJustEnter, bool IncludeLeaving)
{
	if (!Target || !this->GetOwner()) return TArray<FOffendedCaughtResult>();
	
	TArray<FOffendedCaughtResult> Res;
	for (FBeCaughtActorInfo AInfo : Actors)
	{
		//捕获目标不符合条件，就跳过
		int32 BoxKey = FStringPool::Register(AInfo.AttackBoxName);
		if (
			(AInfo.CaughtHitBoxData && AInfo.CaughtHitBoxData->Active == false) || 
			//(AInfo.BeHitBox && AInfo.BeHitBox->Active == false) ||	//TODO：即将干掉
			!AInfo.BeCaughtActor ||
			AInfo.BeCaughtActor != Target ||
			LivingAttackBoxes.Contains(BoxKey) == false ||
			AttackBoxes.Contains(BoxKey) == false ||
			(AInfo.CaughtState == EActorCaughtState::Enter && IncludeJustEnter == false) ||
			(AInfo.CaughtState == EActorCaughtState::Leave && IncludeLeaving == false)
		) continue;
		
		const UCharacterHitBox* CharacterHitBox = Cast<UCharacterHitBox>(AInfo.CaughtHitBoxComponent);
		if (CharacterHitBox && CharacterHitBox->Type == ECharacterHitBoxType::Guard)
		{
			FVector AttackerPos = this->GetOwner()->GetActorLocation();
			const float Deg = UMathFuncLib::GetDegreeBetweenTwoVector(Target->GetActorForwardVector(), AttackerPos - Target->GetActorLocation());
			// UKismetSystemLibrary::PrintString(this, FString::SanitizeFloat(FMath::Abs(Deg)));
			// 伤害盒生效角度，如果攻击actor和受击actor正面的夹角 > 设定的值，则这个伤害盒不起效
			if (FMath::Abs(Deg) > CharacterHitBox->CheckDeg)
			{
				// UKismetSystemLibrary::PrintString(this, "---> Ignore the hit box: " + CharacterHitBox->Id);
				continue;
			}
		}
		
		for (const FOffenseInfo OInfo: WithOffending)
		{
			if (OInfo.AttackHitBoxName.Contains(AInfo.AttackBoxName) && TargetCanBeHitByHitRecord(Target, OInfo.SourceId, OInfo.Index) == true)
			{
				//攻击信息内包含了捕获信息的命中盒子，并且，命中记录里面没有这条，就可以加进结果里面
				Res.Add(FOffendedCaughtResult(OInfo, AInfo, GetAttackBoxByName(AInfo.AttackBoxName)));
			}
		} 
	}
	return Res;
}

TArray<FBeCaughtActorInfo> UAttackHitManager::ThisTickValidTargetInternal(bool MergeSameTarget, bool IncludeJustEnter, bool IncludeLeaving, int32 TargetType)
{
	AActor* ThisActor = Cast<AActor>(this->GetOwner());
	TArray<FBeCaughtActorInfo> Res;

	for (FBeCaughtActorInfo AInfo : Actors)
	{
		// 根据TargetType过滤目标类型
		bool bIsActor = (AInfo.BeCaughtActor != nullptr);
		bool bIsECSMonster = (AInfo.BeCaughtActor == nullptr && AInfo.BeCaughtSubject.IsValid());

		if (TargetType == 1 && !bIsActor) continue;        // 仅传统Actor
		if (TargetType == 2 && !bIsECSMonster) continue;   // 仅ECS怪物
		if (!bIsActor && !bIsECSMonster) continue;          // 无效目标

		//筛选条件不符合，下一条吧
		if ((AInfo.CaughtState == EActorCaughtState::Enter && IncludeJustEnter == false) ||
			(AInfo.CaughtState == EActorCaughtState::Leave && IncludeLeaving == false)) continue;

		//被抓的盒子是否激活了，没激活不算抓住
		if (TargetType == 1 &&(!AInfo.CaughtHitBoxData || !AInfo.CaughtHitBoxComponent || AInfo.CaughtHitBoxData->Active == false)) continue;

		if (const UCharacterHitBox* HitBox = Cast<UCharacterHitBox>(AInfo.CaughtHitBoxComponent))
		{
			const AAwCharacter* Defender = Cast<AAwCharacter>(HitBox->GetOwner());
			FVector AttackerPos = ThisActor->GetActorLocation();
			const float Deg = UMathFuncLib::GetDegreeBetweenTwoVector(Defender->GetActorForwardVector(), AttackerPos - Defender->GetActorLocation());
			// 伤害盒生效角度，如果攻击actor和受击actor正面的夹角 > 设定的值，则这个伤害盒不起效
			if (FMath::Abs(Deg) > HitBox->CheckDeg)
			{
				continue;
			}
		}
		// ECS怪物的特殊检查
		if (bIsECSMonster)
		{
			//检查怪物是否死亡或被标记清理
			if (AInfo.BeCaughtSubject.HasFlag(EFlagmarkBit::C)) continue;
		}

		//判断他被碰到的框是否激活中（顺便判断攻击盒里面是否有，防一手万一需要动态移除或者增加攻击盒）
		int32 BoxKey = FStringPool::Register(AInfo.AttackBoxName);
		if (LivingAttackBoxes.Contains(BoxKey) == false ||
			AttackBoxes.Contains(BoxKey) == false) continue;

		//如果要Merge，就选择优先级高的那个
		bool ToAddNew = true;
		if (MergeSameTarget == true)
		{
			for (int i = 0; i < Res.Num(); i++)
			{
				bool bSameTarget = false;

				// 检查是否是同一个目标
				if (bIsActor && Res[i].BeCaughtActor == AInfo.BeCaughtActor)
				{
					bSameTarget = true;
				}
				else if (bIsECSMonster && Res[i].BeCaughtSubject == AInfo.BeCaughtSubject)
				{
					bSameTarget = true;
				}

				if (bSameTarget)
				{
					// 对于传统Actor，比较HitBox优先级
					if (bIsActor && (!Res[i].CaughtHitBoxData || (AInfo.CaughtHitBoxData && AInfo.CaughtHitBoxData->Priority >= Res[i].CaughtHitBoxData->Priority)))
					{
						Res[i] = AInfo;
					}
					// 对于ECS怪物，简单替换（或者可以根据需要添加其他比较逻辑）
					else if (bIsECSMonster)
					{
						Res[i] = AInfo;
					}
					ToAddNew = false;
					break;
				}
			}
		}
		//没条件了，那就加上吧
		if (ToAddNew == true) Res.Add(AInfo);
	}

	return Res;
}

// 向后兼容的重载版本
TArray<FBeCaughtActorInfo> UAttackHitManager::ThisTickValidTarget(bool MergeSameTarget, bool IncludeJustEnter, bool IncludeLeaving)
{
	return ThisTickValidTargetInternal(MergeSameTarget, IncludeJustEnter, IncludeLeaving, 0);
}

TArray<FBeCaughtActorInfo> UAttackHitManager::ThisTickValidECSTarget(bool MergeSameTarget, bool IncludeJustEnter, bool IncludeLeaving)
{
	// 直接调用统一的ThisTickValidTarget方法，TargetType=2表示仅ECS怪物
	return ThisTickValidTargetInternal(MergeSameTarget, IncludeJustEnter, IncludeLeaving, 2);
}

void UAttackHitManager::ActiveAttackHitBox(FString BoxName)
{
	int32 BoxKey = FStringPool::Register(BoxName);
	if (this->AttackBoxes.Contains(BoxKey))
	{
		this->AttackBoxes[BoxKey]->SetActive(true);
	}
	if (!this->LivingAttackBoxes.Contains(BoxKey) && this->AttackBoxes.Contains(BoxKey))
	{
		LivingAttackBoxes.Add(BoxKey);
	}	
}

void UAttackHitManager::ActiveAllAttackHitBox()
{
	for (auto& Box : AttackBoxes)
	{
		Box.Value->SetActive(true);
		if (!this->LivingAttackBoxes.Contains(Box.Key))
		{
			LivingAttackBoxes.Add(Box.Key);
		}
	}
}
void UAttackHitManager::DeactiveAttackBox(FString BoxName)
{
	int32 BoxKey = FStringPool::Register(BoxName);
	if (this->AttackBoxes.Contains(BoxKey))
	{
		this->AttackBoxes[BoxKey]->SetActive(false);
	}
	LivingAttackBoxes.Remove(BoxKey);
}

void UAttackHitManager::DeactiveAllAttackBox()
{
	for (auto& Box : AttackBoxes)
	{
		Box.Value->SetActive(false);
	}
	LivingAttackBoxes.Empty();
}

UPrimitiveComponent* UAttackHitManager::GetAttackBoxByName(const FString& AttackBoxName)
{
	int32 BoxKey = FStringPool::Register(AttackBoxName);
	return GetAttackBoxByKey(BoxKey);
}

UPrimitiveComponent* UAttackHitManager::GetAttackBoxByKey(const int32 BoxKey)
{
	if (this->AttackBoxes.Contains(BoxKey))
	{
		return AttackBoxes[BoxKey];
	}
	return nullptr;
}

TArray<FString> UAttackHitManager::GetAllActiveAttackBoxNames()
{
	TArray<FString> Result;
	for (int32 BoxKey : LivingAttackBoxes)
	{
		Result.Add(FStringPool::Get(BoxKey));
	}
	return Result;
}

TArray<int32> UAttackHitManager::GetAllActiveAttackBoxKeys()
{
	return this->LivingAttackBoxes;
}

AAwCharacter* UAttackHitManager::GetSourceCharacter()
{
	AAwCharacter* Char = Cast<AAwCharacter>(GetOwner());
	AAwBullet* Blt = Cast<AAwBullet>(GetOwner());
	AAWAoe* Aoe = Cast<AAWAoe>(GetOwner());
	if (AttackBoxType == Melee)
	{
		return Char;
	}
	if (AttackBoxType == Bullet)
	{
		return Blt->Caster;
	}
	if (AttackBoxType == AOE)
	{
		return Aoe->Caster;
	}
	return nullptr;
}

void UAttackHitManager::InitAttackBox()
{
	AAwCharacter* Char = GetSourceCharacter();
	if (!Char)return;
	AAwBullet* Bullet = Cast<AAwBullet>(GetOwner());
	
	TArray<UActorComponent*> AHitBoxes;
	if (!IsValid(this) || (Bullet && !Bullet->Caster))
	{
		// UE_LOG(LogTemp,Log,TEXT("UAttackHitManager is not valid.之前发生在角色重建（因为使用了2次新建角色确保初始化）"));
		return;
	}
	GetOwner()->GetComponents(AHitBoxes, true);
	const auto Mechanism = UMachine::ObtainMechanism(GetWorld());
	for (UActorComponent* HitBox : AHitBoxes)
	{
		UAttackHitBoxSign* HitBoxSign = HitBox->GetAssetUserData<UAttackHitBoxSign>();
		if (!HitBoxSign)
		{
			continue;
		}
		if (HitBoxSign->Name.IsEmpty()) HitBoxSign->Name = HitBox->GetName();
		int32 BoxKey = FStringPool::Register(HitBoxSign->Name);

		UPrimitiveComponent* Comp = Cast<UPrimitiveComponent>(HitBox);
		if (!Comp) continue;

		// 使用AddAttackHitBox来避免重复绑定事件
		AddAttackHitBox(BoxKey, Comp);
		SetupECSCollision(Char,HitBox,Comp,Mechanism,BoxKey);
	}
}


void UAttackHitManager::ActiveECSAttack(const FString& BoxName,EFlagmarkBit HitFlag)
{
	int32 BoxKey = FStringPool::Register(BoxName);
	if(!AttackBoxEntity.Contains(BoxKey))
	{
		UE_LOG(LogTemp,Error,TEXT("%s not found! Check AttackBoxName in montage!"),*BoxName);
		return;
	}
	if (AttackBoxEntity[BoxKey].GetTraitPtr<FTrAttackEnable,EParadigm::Unsafe>())
	{
		return;
	}
	FTrAttackBox* attackbox  = AttackBoxEntity[BoxKey].GetTraitPtr<FTrAttackBox, EParadigm::Unsafe>();
	attackbox->AttackFlag = HitFlag;
	AttackBoxEntity[BoxKey]->SetTrait(FTrAttackEnable());
}

void UAttackHitManager::DeactiveECSAttack(const FString& BoxName)
{
	int32 BoxKey = FStringPool::Register(BoxName);
	if(!AttackBoxEntity.Contains(BoxKey))
	{
		UE_LOG(LogTemp,Error,TEXT("%s not found! Check AttackBoxName in montage!"),*BoxName);
		return;
	}
	if (AttackBoxEntity[BoxKey].IsValid())
	{
		AttackBoxEntity[BoxKey]->RemoveTrait<FTrAttackEnable>();
	}
}
EFlagmarkBit UAttackHitManager::GetECSAttackFlag(const FString& BoxName)
{
	int32 BoxKey = FStringPool::Register(BoxName);
	if(!AttackBoxEntity.Contains(BoxKey))
	{
		UE_LOG(LogTemp,Error,TEXT("%s not found! Check AttackBoxName in montage!"),*BoxName);
		return EFlagmarkBit::E;
	}
	if (AttackBoxEntity[BoxKey].IsValid())
	{
		return AttackBoxEntity[BoxKey]->GetTraitPtr<FTrAttackBox,EParadigm::Unsafe>()->AttackFlag;
	}
	return EFlagmarkBit::E;
}

void UAttackHitManager::SyncECSAttackBoxPosition(const int32& BoxKey)
{
	if (!AttackBoxEntity.Contains(BoxKey))
	{
		return;
	}
	auto AttackBoxLoc = AttackBoxEntity[BoxKey]->GetTraitPtr<FLocated,EParadigm::Unsafe>();
	AttackBoxLoc->Location = AttackBoxes[BoxKey]->GetComponentLocation();
	auto AttackBox = AttackBoxEntity[BoxKey]->GetTraitPtr<FTrAttackBox,EParadigm::Unsafe>();
	AttackBox->bIsECSPositionSynced = true;
}

void UAttackHitManager::CheckECSMonsterHit()
{
	// 获取所有激活的攻击盒子
	auto AtkBoxKeys = GetAllActiveAttackBoxKeys();

	// 收集当前帧所有范围内的ECS怪物
	TSet<FSubjectHandle> CurrentFrameMonsters;
	TMap<FSubjectHandle, FString> MonsterToBoxName;

	for (const auto BoxKey : AtkBoxKeys)
	{
		// 获取攻击盒子的位置和半径
		if (!AttackBoxes.Contains(BoxKey) || !AttackBoxEntity.Contains(BoxKey) || !AttackBoxes.Contains(BoxKey))
		{
			continue;
		}
		UPrimitiveComponent* AttackBox = AttackBoxes[BoxKey];
		if (!AttackBox || !AttackBox->IsActive())
		{
			continue;
		}

		FString BoxName = FStringPool::Get(BoxKey);

		auto AtkBox = AttackBoxEntity[BoxKey].GetTraitPtr<FTrAttackBox,EParadigm::Unsafe>();
		FVector BoxLocation = AttackBoxEntity[BoxKey]->GetTraitPtr<FLocated,EParadigm::Unsafe>()->Location;
		float BoxRadius = AtkBox->Radius;


		// 使用ABubbleCage::GetOverlapping查询ECS怪物
		const auto EnemyFilter = FFilter::Make<FEnemy>();
		auto OverlappingMonsters = ABubbleCage::GetOverlapping(BoxLocation, BoxRadius, EnemyFilter);

		// 处理每个重叠的ECS怪物
		for (const auto& Monster : OverlappingMonsters)
		{
			if (!Monster.IsValid())
			{
				continue;
			}

			// 检查怪物是否死亡或被标记清理
			if (Monster.HasFlag(EFlagmarkBit::C))
			{
				continue;
			}

			// 记录当前帧的怪物和对应的攻击盒
			CurrentFrameMonsters.Add(Monster);
			MonsterToBoxName.Add(Monster, BoxName);

			// 检查是否已经在列表中
			bool bFoundInList = false;
			for (auto& AInfo : Actors)
			{
				// 检查ECS怪物
				if (AInfo.BeCaughtActor == nullptr && AInfo.BeCaughtSubject.IsValid())
				{
					if (AInfo.BeCaughtSubject == Monster && AInfo.AttackBoxName == BoxName)
					{
						bFoundInList = true;
						// 如果之前是Leave状态，现在重新进入范围，改为Enter状态
						if (AInfo.CaughtState == EActorCaughtState::Leave)
						{
							AInfo.CaughtState = EActorCaughtState::Enter;
							AInfo.DelayDuration = 0;
						}
						break;
					}
				}
			}

			// 如果不在列表中，添加新的怪物
			if (!bFoundInList)
			{
				AddECSMonster(BoxName, Monster, 0.1f); // 默认0.1秒延迟
			}
		}
	}

	// 检查列表中的ECS怪物，如果不在当前帧的范围内，标记为Leave
	for (auto& AInfo : Actors)
	{
		// 只处理ECS怪物
		if (AInfo.BeCaughtActor == nullptr && AInfo.BeCaughtSubject.IsValid())
		{
			if (AInfo.CaughtState != EActorCaughtState::Leave)
			{
				bool bStillInRange = false;
				for (const auto& Monster : CurrentFrameMonsters)
				{
					if (AInfo.BeCaughtSubject == Monster && MonsterToBoxName.Contains(Monster))
					{
						if (MonsterToBoxName[Monster] == AInfo.AttackBoxName)
						{
							bStillInRange = true;
							break;
						}
					}
				}

				if (!bStillInRange)
				{
					AInfo.CaughtState = EActorCaughtState::Leave;
				}
			}
		}
	}
}

// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "TheAwakener_FO/GamePlay/MonsterSetupTool/Core/MonsterSetupComponent.h"
#include "TheAwakener_FO/GamePlay/MonsterSetupTool/Core/MonsterWeightGroupAsset.h"
#include "TheAwakener_FO/GamePlay/MonsterSetupTool/Core/MonsterSetupTypes.h"
#include "MonsterSetupBlueprintLibrary.generated.h"

/**
 * 怪物设置蓝图函数库
 * 提供完整的蓝图接口用于怪物设置工具
 */
UCLASS()
class THEAWAKENER_FO_API UMonsterSetupBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // ========== 组件创建和管理 ==========

    /**
     * 在指定位置创建怪物设置组件
     * @param TargetActor 目标Actor
     * @param Location 位置
     * @param GroupName 分组名称
     * @param WeightGroupName 权重组名称
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static UMonsterSetupComponent* CreateMonsterSetupAtLocation(
        AActor* TargetActor,
        const FVector& Location,
        const FString& GroupName = TEXT("DefaultGroup"),
        const FString& WeightGroupName = TEXT("基础")
    );

    /**
     * 批量创建怪物设置组件
     * @param Locations 位置列表
     * @param GroupName 分组名称
     * @param WeightGroupName 权重组名称
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static TArray<UMonsterSetupComponent*> BatchCreateMonsterSetups(
        const TArray<FVector>& Locations,
        const FString& GroupName = TEXT("DefaultGroup"),
        const FString& WeightGroupName = TEXT("基础")
    );

    /**
     * 复制怪物设置到多个位置
     * @param SourceComponent 源组件
     * @param Locations 目标位置列表
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static TArray<UMonsterSetupComponent*> CopyMonsterSetupToLocations(
        UMonsterSetupComponent* SourceComponent,
        const TArray<FVector>& Locations
    );

    // ========== 权重组管理 ==========

    /**
     * 创建预设权重组
     * @param PresetType 预设类型
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static UMonsterWeightGroupAsset* CreatePresetWeightGroup(EPresetWeightGroupType PresetType);

    /**
     * 创建自定义权重组
     * @param GroupName 组名称
     * @param WeightConfigs 权重配置列表
     * @param Description 描述
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static UMonsterWeightGroupAsset* CreateCustomWeightGroup(
        const FString& GroupName,
        const TArray<FMonsterWeightConfig>& WeightConfigs,
        const FString& Description = TEXT("")
    );

    // ========== 查询功能 ==========

    /**
     * 获取世界中所有怪物设置组件
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static TArray<UMonsterSetupComponent*> GetAllMonsterSetupComponents(const UObject* WorldContext);

    /**
     * 按分组获取怪物设置组件
     * @param WorldContext 世界上下文
     * @param GroupName 分组名称
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static TArray<UMonsterSetupComponent*> GetMonsterSetupComponentsByGroup(const UObject* WorldContext, const FString& GroupName);

    /**
     * 获取所有分组名称
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static TArray<FString> GetAllGroupNames(const UObject* WorldContext);

    /**
     * 获取世界统计信息
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static FMonsterSetupStatistics GetWorldStatistics(const UObject* WorldContext);

    /**
     * 获取世界统计信息（字符串格式）
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static FString GetWorldStatisticsString(const UObject* WorldContext);

    // ========== 批量操作 ==========

    /**
     * 生成所有怪物
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 GenerateAllMonsters(const UObject* WorldContext);

    /**
     * 生成指定分组的怪物
     * @param WorldContext 世界上下文
     * @param GroupName 分组名称
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 GenerateMonstersForGroup(const UObject* WorldContext, const FString& GroupName);

    /**
     * 清除所有怪物
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 ClearAllMonsters(const UObject* WorldContext);

    /**
     * 清除指定分组的怪物
     * @param WorldContext 世界上下文
     * @param GroupName 分组名称
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 ClearMonstersForGroup(const UObject* WorldContext, const FString& GroupName);

    /**
     * 获取总生成怪物数量
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 GetTotalSpawnedMonsterCount(const UObject* WorldContext);

    /**
     * 获取总活跃怪物数量
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 GetTotalActiveMonsterCount(const UObject* WorldContext);

    // ========== 工具功能 ==========

    /**
     * 检查位置是否有效（可以生成怪物）
     * @param WorldContext 世界上下文
     * @param Location 检查位置
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static bool IsValidMonsterLocation(const UObject* WorldContext, const FVector& Location);

    /**
     * 查找最近的有效位置
     * @param WorldContext 世界上下文
     * @param Location 起始位置
     * @param SearchRadius 搜索半径
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static FVector FindNearestValidLocation(const UObject* WorldContext, const FVector& Location, float SearchRadius = 200.0f);

    /**
     * 获取可用的怪物类型列表
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static TArray<FMonsterTypeMapping> GetAvailableMonsterTypes();

    // ========== 验证和调试 ==========

    /**
     * 验证所有组件配置
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static TArray<FString> ValidateAllComponentConfigurations(const UObject* WorldContext);

    /**
     * 打印组件信息
     * @param Component 组件
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static void PrintComponentInfo(UMonsterSetupComponent* Component);

    /**
     * 打印世界怪物设置摘要
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static void PrintWorldMonsterSetupSummary(const UObject* WorldContext);

    /**
     * 绘制调试信息
     * @param WorldContext 世界上下文
     * @param Duration 持续时间
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static void DrawDebugInfo(const UObject* WorldContext, float Duration = 5.0f);

    /**
     * 清理所有无效的怪物引用
     * @param WorldContext 世界上下文
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup", meta = (WorldContext = "WorldContext"))
    static int32 CleanupAllInvalidMonsterReferences(const UObject* WorldContext);

    // ========== 预设配置 ==========

    /**
     * 设置守卫哨所配置
     * @param Component 组件
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static void SetupGuardPostConfiguration(UMonsterSetupComponent* Component);

    /**
     * 设置区域防守配置
     * @param Component 组件
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static void SetupAreaDefenseConfiguration(UMonsterSetupComponent* Component);

    /**
     * 设置精英怪物配置
     * @param Component 组件
     */
    UFUNCTION(BlueprintCallable, Category = "Monster Setup")
    static void SetupEliteMonsterConfiguration(UMonsterSetupComponent* Component);

private:
    /**
     * 获取世界对象
     * @param WorldContext 世界上下文
     */
    static UWorld* GetWorldFromContext(const UObject* WorldContext);

    /**
     * 创建临时Actor用于承载组件
     * @param World 世界对象
     * @param Location 位置
     * @param Name Actor名称
     */
    static AActor* CreateTempActorForComponent(UWorld* World, const FVector& Location, const FString& Name);
};

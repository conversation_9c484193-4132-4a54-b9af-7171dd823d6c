// Fill out your copyright notice in the Description page of Project Settings.


#include "AwGameModeBase.h"
#include "Kismet/GameplayStatics.h"
#include "AwGameState.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "Materials/MaterialInstanceConstant.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Survivor/SurvivorEventState.h"



void AAwGameModeBase::BeginPlay()
{
	Super::BeginPlay();
	
	InitPostProcessVolume();
	UGameplayFuncLib::GetScoreManager()->ResetKillCombo();
}

void AAwGameModeBase::GetSeamlessTravelActorList(bool bToTransition, TArray<AActor*>& ActorList)
{
	Super::GetSeamlessTravelActorList(bToTransition, ActorList);
}

void AAwGameModeBase::HandleStartingNewPlayer_Implementation(APlayerController* NewPlayer)
{
	Super::HandleStartingNewPlayer_Implementation(NewPlayer);
	AAwPlayerController* NewPlayerController = Cast<AAwPlayerController>(NewPlayer);
	if (NewPlayerController)
	{
		UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
		if (!HostPlayerController)
		{
			HostPlayerController = NewPlayerController;
			if (CurGameInstance)
				CurGameInstance->HostPlayerController = NewPlayerController;
		}
		NewPlayerInit(NewPlayerController);
		AAwGameState* CurGameState = Cast<AAwGameState>(UGameplayStatics::GetGameState(GWorld));
		if (CurGameState && CurGameInstance)
		{
			if (CurGameInstance->bNetworked)
			{
				if (CurGameState->PlayerArray.Num() <= CurGameInstance->CurSeesionInfo.MaxPublicPlayerNum)
				{
					CurGameInstance->TeamManager->AddPlayer(NewPlayerController);

					CurGameInstance->CurSeesionInfo.CurPublicPlayerNum = CurGameState->PlayerArray.Num();
					UpdateSessionInfoToAllClient(CurGameInstance->CurSeesionInfo);
				}
				else
				{
					NewPlayerController->BreakOnlineLink("连接已断开");
				}
			}
			else
			{
				CurGameInstance->TeamManager->AddPlayer(NewPlayerController);
			}
			
		}
	}
}

void AAwGameModeBase::Logout(AController* Logout)
{
	Super::Logout(Logout);
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		AAwPlayerController* LogoutPlayer = Cast<AAwPlayerController>(Logout);
		if (LogoutPlayer)
			CurGameInstance->TeamManager->DeletePlayer(LogoutPlayer);
		if (CurGameInstance->bNetworked)
		{
			CurGameInstance->CurSeesionInfo.CurPublicPlayerNum--;
			if(CurGameInstance->TeamManager->PlayerList.Num())
				UpdateSessionInfoToAllClient(CurGameInstance->CurSeesionInfo);
		}
	}
}
//在EnemySpawner和GameModeRogue里，怪物死亡时调用
//GM相关功能在MobDeadFunc里重写
//其他组件的功能在OnMobDeadDelegate里绑定
void AAwGameModeBase::MobDeadBase(FMobDeadInfo DeadInfo)
{
	MobDeadFunc(DeadInfo);
	OnMobDeadDelegate.Broadcast(DeadInfo);
}

void AAwGameModeBase::MobSpawnBase(FMobDeadInfo Info)
{
	OnMobSpawnDelegate.Broadcast(Info);
}

void AAwGameModeBase::NewPlayerInit_Implementation(AAwPlayerController* NewPlayer)
{

}

void AAwGameModeBase::UpdateSessionInfoToAllClient(FAwSessionInfo CurSessionInfo)
{
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	
	for (int i = 0; i < CurGameInstance->TeamManager->PlayerList.Num(); i++)
	{
		CurGameInstance->TeamManager->PlayerList[i]->UpdateSessionInfoToCurClient(CurSessionInfo);
	}	
}

APostProcessVolume* AAwGameModeBase::InitPostProcessVolume()
{
	FActorSpawnParameters SpawnInfo;
	SpawnInfo.Instigator = nullptr;
	SpawnInfo.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
	UWorld* World = GetWorld();
	APostProcessVolume* PostProcessVolume = World->SpawnActor<APostProcessVolume>(SpawnInfo);
	if (PostProcessVolume)
	{
		PostProcessVolume->bEnabled = true;
		PostProcessVolume->bUnbound = true;
		FPostProcessSettings& PostProcessSettings = PostProcessVolume->Settings;
	
		UMaterialInstance* Post_RadialBlur_Inst = LoadObject<UMaterialInstance>(nullptr,
			TEXT("UMaterialInstance'/Game/MaterialLibrary/Debug/MI_PP_RadialBlur.MI_PP_RadialBlur'"));
		if (Post_RadialBlur_Inst)
		{
			Post_RadialBlur_InsDyna = UKismetMaterialLibrary::CreateDynamicMaterialInstance(this, Post_RadialBlur_Inst);
			const FWeightedBlendable RadialBlurWeightedBlendable = FWeightedBlendable(1, Post_RadialBlur_InsDyna);
			PostProcessSettings.WeightedBlendables.Array.Add(RadialBlurWeightedBlendable);
		}

		/*
		UMaterialInstanceConstant* Post_Outline_Inst = LoadObject<UMaterialInstanceConstant>(nullptr,
			TEXT("MaterialInstanceConstant'/Game/MaterialLibrary/Debug/MPP_OutLine_Inst.MPP_OutLine_Inst'"));
		if (Post_Outline_Inst)
		{
			Post_Outline_InsDyna = UKismetMaterialLibrary::CreateDynamicMaterialInstance(this, Post_Outline_Inst);
			const FWeightedBlendable OutlineWeightedBlendable = FWeightedBlendable(1, Post_Outline_InsDyna);
			PostProcessSettings.WeightedBlendables.Array.Add(OutlineWeightedBlendable);
		}
		*/
		
		UMaterialInstanceConstant* Post__LensDistortion_Inst = LoadObject<UMaterialInstanceConstant>(nullptr,
			TEXT("UMaterialInstance'/Game/MaterialLibrary/Common/PostProcessVolume/PP_LensDistortion_Inst.PP_LensDistortion_Inst'"));
		if (Post__LensDistortion_Inst)
		{
			Post_LensDistortion_InsDyna = UKismetMaterialLibrary::CreateDynamicMaterialInstance(this, Post__LensDistortion_Inst);
			const FWeightedBlendable LensDistortionWeightedBlendable = FWeightedBlendable(1, Post_LensDistortion_InsDyna);
			PostProcessSettings.WeightedBlendables.Array.Add(LensDistortionWeightedBlendable);
		}
	}

	return PostProcessVolume;
}

TArray<FString> AAwGameModeBase::GetMobLootPackages_Implementation()
{
	return TArray<FString>();
}

TArray<FString> AAwGameModeBase::GetChestLootPackages_Implementation()
{
	return TArray<FString>();
}

void AAwGameModeBase::TickSetRadiaBlurValue(float DeltaSeconds)
{
	if (!Post_RadialBlur_InsDyna ||
		RadioBlurChangedTime >= RadiaBlurChangeDuration)
		return;

	RadioBlurChangedTime += DeltaSeconds;
	//if (RadioBlurChangedTime > RadiaBlurChangeDuration)
	//	RadioBlurChangedTime = RadiaBlurChangeDuration;
	
	float RadiaBlurValue;
	float Dis = ToRadiaBlurValue-OriRadiaBlurValue;
	if (RadiaBlurChangeOnPingPong)
	{
		if (RadioBlurChangedTime<=RadiaBlurChangeDuration/2)
			RadiaBlurValue = this->OriRadiaBlurValue + Dis/(RadiaBlurChangeDuration/2) * RadioBlurChangedTime;
		else
			RadiaBlurValue = this->OriRadiaBlurValue + (-Dis/(RadiaBlurChangeDuration/2)*RadioBlurChangedTime + 2*Dis);
	}
	else
		RadiaBlurValue = this->OriRadiaBlurValue + Dis/RadiaBlurChangeDuration * RadioBlurChangedTime;

	if (RadioBlurChangedTime > RadiaBlurChangeDuration)RadiaBlurValue = 0;	//现在过时老子就给你设置为0

	Post_RadialBlur_InsDyna->SetScalarParameterValue("Blur", RadiaBlurValue);
}

void AAwGameModeBase::AddLevelUpRemain(int levelUP)
{
	this->LevelUpRemain += levelUP;
	LevelUped = true;
	if (this->LevelUpRemain<0)
		this->LevelUpRemain = 0;
}

void AAwGameModeBase::SyncDataToGameState(AAwGameState* AwGameState)
{
}

void AAwGameModeBase::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
	UAwGameInstance* CurGameInstance = UGameplayFuncLib::GetAwGameInstance();
	if (CurGameInstance)
	{
		//GameInstanceTick
		CurGameInstance->Tick(DeltaSeconds);
	}
	TickSetRadiaBlurValue(DeltaSeconds);

	AAwGameState* CurGameState = UGameplayFuncLib::GetAwGameState();
	if (CurGameState)
	{
		for (int i = 0; i < CurGameState->DurationParticleCompList.Num(); i++)
			CurGameState->DurationParticleCompList[i].Timer += DeltaSeconds;
		
		int i = 0;
		while (i < CurGameState->DurationParticleCompList.Num())
		{
			if(CurGameState->DurationParticleCompList[i].Timer >= CurGameState->DurationParticleCompList[i].Duration + 1)
			{
				if (CurGameState->DurationParticleCompList[i].ThisParticle)
					CurGameState->DurationParticleCompList[i].ThisParticle->DestroyComponent();
				CurGameState->DurationParticleCompList.RemoveAt(i);
			}
			else if (CurGameState->DurationParticleCompList[i].Timer >= CurGameState->DurationParticleCompList[i].Duration &&
				!CurGameState->DurationParticleCompList[i].IdDeactivate)
			{
				if(CurGameState->DurationParticleCompList[i].ThisParticle)
					CurGameState->DurationParticleCompList[i].ThisParticle->Deactivate();
				CurGameState->DurationParticleCompList[i].IdDeactivate = true;
				i++;
			}
			else
				i++;
		}
	}
}

/**
* 动态模糊
* @param ToBlur 模糊程度，数字越大越模糊，4已经非常模糊了，建议1-3。
* @param Duration 持续时间（秒）
* @param IsPingPong 是否是来回的，如果是，会从清楚的变模糊在变回来，时间对半开
* @param IsFromStart 是否启用StartBlur
* @param StartBlur 开始的时间（秒）
*/
void AAwGameModeBase::SetRadialBlur(float ToBlur, float Duration, bool IsPingPong, bool IsFromStart, float StartBlur)
{
	if (!Post_RadialBlur_InsDyna)
		return;
	
	this->RadioBlurChangedTime = 0;
	if (Duration <= 0)
	{
		Post_RadialBlur_InsDyna->SetScalarParameterValue("Blur", ToBlur);
		this->RadiaBlurChangeDuration = 0;
		return;
	}

	this->ToRadiaBlurValue = ToBlur;
	this->RadiaBlurChangeDuration = Duration;
	this->RadiaBlurChangeOnPingPong = IsPingPong;
	if (IsFromStart)
	{
		Post_RadialBlur_InsDyna->SetScalarParameterValue("Blur", StartBlur);
		this->OriRadiaBlurValue = StartBlur;
	}
	else
		this->OriRadiaBlurValue = Post_RadialBlur_InsDyna->K2_GetScalarParameterValue("Blur");
}

bool AAwGameModeBase::CheckEventCondition(FString jsonEvent, int eventIndex)
{
	FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(jsonEvent);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
	if (Func)
	{
		struct
		{
			int eventIndex;
			TArray<FString> Params;
			bool Result;
		}FuncParam;
		FuncParam.eventIndex = eventIndex;
		FuncParam.Params = JsonFunc.Params;
		ProcessEvent(Func, &FuncParam);
		//UE_LOG(LogTemp, Warning, TEXT("Event Id %i Condition Match %s"), eventIndex, FuncParam.Result ? TEXT("True") : TEXT("False"));
		return FuncParam.Result;
	}
	return  false;
}

bool AAwGameModeBase::CheckEventConditions(TArray<FString> jsonEvent, int eventIndex)
{
	for (int i = 0; i < jsonEvent.Num(); i++)
	{
		bool tRes = CheckEventCondition(jsonEvent[i],eventIndex);
		if (!tRes)return  false;
	}
	return true;
}

TArray<FTransform> AAwGameModeBase::CallPositionFunc(FString jsonEvent)
{
	TArray<FTransform> result;
	FJsonFuncData JsonFunc = UCallFuncLib::StringToJsonFuncData(jsonEvent);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFunc);
	float SpawnOffset = 600;
	if (Func)
	{
		struct
		{
			TArray<FString> Params;
			TArray<FTransform> Result;
		}FuncParam;
		FuncParam.Params = JsonFunc.Params;
		ProcessEvent(Func, &FuncParam);
		for (auto trans: FuncParam.Result)
		{
			FVector location = trans.GetLocation();
			location.Y += SpawnOffset;
			trans.SetLocation(location);
		}
		// UE_LOG(LogTemp, Warning, TEXT("DialogModel Id(Condition Match): %s"), *DialogModel.Value.Id);
		return FuncParam.Result;
	}
	return result;
}

void AAwGameModeBase::LogEventTriggered(int eventIndex)
{
	
	auto gs = UGameplayFuncLib::GetAwGameStateSurvivor();
	// gs->GetPlayTime() - gs->RoundStartTime
	if(gs && gs->EventStates.Contains(eventIndex))
	{
		gs->EventStates[eventIndex].LastTriggerTime = gs->GetPlayTime();
	}
	UE_LOG(LogTemp, Warning, TEXT("Event No.%i not found!"), eventIndex);
}

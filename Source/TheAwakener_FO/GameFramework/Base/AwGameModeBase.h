// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameMode.h"
#include "AwGameInstance.h"
#include "AwGameState.h"
#include "AwMobDeadInfo.h"
#include "Engine/PostProcessVolume.h"
#include "AwGameModeBase.generated.h"

/**
 * 
 */
//PlayerCharacterInit后的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerCharacterInitDelegate, AAwCharacter*, PlayerCharacter);
//ChangePawn后的动态多播委托
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerChangePawnDelegate, AAwCharacter*, PlayerCharacter);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMobDeadDelegate, FMobDeadInfo, DeadInfo);
UCLASS()
class THEAWAKENER_FO_API AAwGameModeBase : public AGameMode
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwPlayerController* HostPlayerController;

	virtual void BeginPlay() override;
	
public:
	virtual void GetSeamlessTravelActorList(bool bToTransition, TArray<AActor*>& ActorList) override;

	virtual void HandleStartingNewPlayer_Implementation(APlayerController* NewPlayer) override;

	virtual void Logout(AController* Exiting) override;

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void NewPlayerInit(AAwPlayerController* NewPlayer);
	//由蓝图实现作为框架
	UFUNCTION(BlueprintCallable)
	void MobDeadBase(FMobDeadInfo DeadInfo);
	UFUNCTION(BlueprintCallable)
	void MobSpawnBase(FMobDeadInfo Info);
	//由蓝图实现作为子类扩展
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void MobDeadFunc(FMobDeadInfo DeadInfo);
    // 在蓝图里可见并可绑定
    UPROPERTY(BlueprintAssignable, Category="Events")
    FOnMobDeadDelegate OnMobDeadDelegate;
    UPROPERTY(BlueprintAssignable, Category="Events")
    FOnMobDeadDelegate OnMobSpawnDelegate;
	UFUNCTION(BlueprintCallable)
	void UpdateSessionInfoToAllClient(FAwSessionInfo CurSessionInfo);

	//获取怪物掉落Id
	UFUNCTION(BlueprintNativeEvent)
	TArray<FString> GetMobLootPackages();
	//获取宝箱掉落Id
	UFUNCTION(BlueprintNativeEvent)
	TArray<FString> GetChestLootPackages();
	
	// ---------- PostProcess ----------
	/**
	 * 创建并初始化后期处理盒子 PostProcessVolume
	 */
	UFUNCTION(BlueprintCallable)
	APostProcessVolume* InitPostProcessVolume();

	/**
	 * 屏幕后期特效材质-径向模糊
	 * 如何修改：
	 *     Post_RadialBlur_InsDyna->SetScalarParameterValue("Blur", 1);
	 */
	UPROPERTY(BlueprintReadWrite)
	UMaterialInstanceDynamic* Post_RadialBlur_InsDyna;
	
	/**
	 * 屏幕后期特效材质-外边框
	 */
	UPROPERTY(BlueprintReadWrite)
	UMaterialInstanceDynamic* Post_Outline_InsDyna;

	/**
	 * 屏幕后期特效材质-径向扭曲
	 */
	UPROPERTY(BlueprintReadWrite)
	UMaterialInstanceDynamic* Post_LensDistortion_InsDyna;

	UFUNCTION()
	void TickSetRadiaBlurValue(float DeltaSeconds);

	UPROPERTY()
	float ToRadiaBlurValue = 0;
	UPROPERTY()
	float OriRadiaBlurValue = 0;
	UPROPERTY()
	float RadioBlurChangedTime = 0;
	UPROPERTY()
	float RadiaBlurChangeDuration = 0;
	UPROPERTY()
	bool RadiaBlurChangeOnPingPong = false;
	// ---------- ---------- ----------
	//等级提升后，未处理的内容
	UPROPERTY(BlueprintReadWrite)
	int LevelUpRemain = 0;
	UPROPERTY(BlueprintReadWrite)
	bool LevelUped = 0;
	
public:

	UFUNCTION(BlueprintCallable)
	void AddLevelUpRemain(int levelUP);
	virtual void SyncDataToGameState(AAwGameState* AwGameState);

	virtual void Tick(float DeltaSeconds) override;

	UFUNCTION(BlueprintCallable)
	virtual void PlayerClientLoadFinish(AAwPlayerController* Player) {}
	
	UFUNCTION(BlueprintCallable)
	void SetRadialBlur(float ToBlur, float Duration, bool IsPingPong = false, bool IsFromStart = false, float StartBlur = 0);

	UPROPERTY(BlueprintAssignable,BlueprintCallable, Category = "Events")
	FOnPlayerCharacterInitDelegate OnPlayerCharacterInit;

	UPROPERTY(BlueprintAssignable,BlueprintCallable, Category = "Events")
	FOnPlayerChangePawnDelegate OnPlayerChangePawn;
	// Survivor 专用
public:
	UFUNCTION(BlueprintCallable, Category="Survivor")
	bool CheckEventCondition(FString jsonEvent,int eventIndex);
	UFUNCTION(BlueprintCallable, Category="Survivor")
	bool CheckEventConditions(TArray<FString> jsonEvent,int eventIndex);
	UFUNCTION(BlueprintCallable, Category="Survivor")
	TArray<FTransform> CallPositionFunc(FString jsonEvent);
	UFUNCTION(BlueprintCallable, Category="Survivor")
	void LogEventTriggered(int eventIndex);
};

// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "PlatformCustomNodes.h"
#if USING_CODE_ANALYSIS
MSVC_PRAGMA(warning(push))
MSVC_PRAGMA(warning(disable : ALL_CODE_ANALYSIS_WARNINGS))
#endif // USING_CODE_ANALYSIS
#pragma warning(push)
#pragma warning(disable:4996)
#pragma push_macro("ARRAY_COUNT")
#undef ARRAY_COUNT
#include "ThirdParty/Steamworks/Steamv153/sdk/public/steam/steam_api.h"
#pragma pop_macro("ARRAY_COUNT")
#pragma warning(pop)

bool UPlatformCustomNodes::BSteamIsDlcInstalled(int32 AppID)
{
	if (SteamUser() != nullptr)
	{
	bool Out = SteamApps()->BIsDlcInstalled(AppID);
	return Out;
	}
	return false;
}

bool UPlatformCustomNodes::BSteamIsAppInstalled(int32 AppID)
{
	if (SteamUser() != nullptr)
	{
	bool Out = SteamApps()->BIsAppInstalled(AppID);
	return Out;
	}
	return false;
}

void UPlatformCustomNodes::SteamOpenStore(int32 DLC_AppID)
{
	if (SteamUser() != nullptr)
	{
	// SteamFriends()->ActivateGameOverlayToWebPage("https://store.steampowered.com/");
	SteamFriends()->ActivateGameOverlayToStore(DLC_AppID, k_EOverlayToStoreFlag_None);
	}
}

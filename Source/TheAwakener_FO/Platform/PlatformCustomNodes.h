// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once
#pragma warning(disable:4996)
#pragma warning(disable:4828)
#pragma warning(disable:4265)
#pragma push_macro("ARRAY_COUNT")
#undef ARRAY_COUNT
#pragma pop_macro("ARRAY_COUNT")
#pragma warning(pop)
#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "PlatformCustomNodes.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPlatformCustomNodes : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable, META = (DisplayName = "Has DLC Installed", CompactNodeTitle = "Has DLC Installed", Category = "Steam Custom Nodes"))
	static bool BSteamIsDlcInstalled(int32 AppID);

	UFUNCTION(BlueprintCallable, META = (DisplayName = "Game Installed", CompactNodeTitle = "Game Installed", Category = "Steam Custom Nodes"))
	static bool BSteamIsAppInstalled(int32 AppID);

	UFUNCTION(BlueprintCallable, META = (DisplayName = "Open Store", CompactNodeTitle = "Open Store", Category = "Steam Custom Nodes"))
	static void SteamOpenStore(int32 DLC_AppID);
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/ProgressBar.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "BossHealthUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBossHealthUI : public UUserWidget
{
	GENERATED_BODY()
private:
	AAw<PERSON>haracter* Boss;
	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UProgressBar* HpBar;

	UFUNCTION(BlueprintCallable)
	void SetBossCharacter(AAwCharacter* Character){	Boss = Character; }

	UFUNCTION(BlueprintImplementableEvent,BlueprintCallable)
	void RefreshUI();
};

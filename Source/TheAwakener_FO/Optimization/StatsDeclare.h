#pragma once

DECLARE_STATS_GROUP(TEXT("TheAwakenerSurvivor"), STATGROUP_TheAwakenerSurvivor, STATCAT_Advanced);
DECLARE_CYCLE_STAT_EXTERN(TEXT("RenderState"),
						  STAT_SurvivorRenderState,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorAfterSpawn"),
						  STAT_SurvivorAfterSpawn,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorDecouple"),
						  STAT_SurvivorDecouple,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorHit"),
						  STAT_SurvivorHit,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);

DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorGroundCheck"),
						  STAT_SurvivorGroundCheck,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorMovement"),
						  STAT_SurvivorMovement,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorForceMovement"),
						  STAT_SurvivorForceMovement,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);

DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorReplace_Search"),
						  STAT_SurvivorReplace_Search,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Survivor_Approach"),
						  STAT_Survivor_Approach,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorReplace"),
						  STAT_SurvivorReplace,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);

DECLARE_CYCLE_STAT_EXTERN(TEXT("Survivor_Active"),
						  STAT_Survivor_Active,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
//SCOPE_CYCLE_COUNTER(STAT_Survivor_Active);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Survivor_FaceTo"),
						  STAT_Survivor_FaceTo,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
// DEFINE_STAT(STAT_Survivor_FaceTo);
//SCOPE_CYCLE_COUNTER(STAT_Survivor_FaceTo);
DECLARE_CYCLE_STAT_EXTERN(TEXT("SurvivorReplace_CreateSearchResult"),
						  STAT_SurvivorReplace_CreateSearchResult,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);


DECLARE_CYCLE_STAT_EXTERN(TEXT("Survivor_AOE_Hit_Actor"),
						  STAT_Survivor_AOE_Hit_Actor,
						  STATGROUP_TheAwakenerSurvivor,
						  THEAWAKENER_FO_API);
// DEFINE_STAT(Survivor_AOE_Hit_Actor);
//SCOPE_CYCLE_COUNTER(Survivor_AOE_Hit_Actor);